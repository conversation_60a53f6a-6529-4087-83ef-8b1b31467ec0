import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { placesAutoComplete } from '../../../redux/apis/common';
import { useAppSelector } from '../../../redux/hooks';
import { PlaceResult, SearchState } from '../types';

const SEARCH_DEBOUNCE_MS = 300;
const MIN_SEARCH_LENGTH = 2;

export const useLocationSearch = () => {
	const dispatch = useDispatch();
	const places = useAppSelector(state => state.common.places);
	const debounceRef = useRef<NodeJS.Timeout>();

	const [searchState, setSearchState] = useState<SearchState>({
		query: '',
		results: [],
		isLoading: false,
		error: null
	});

	const debouncedSearch = useCallback((query: string) => {
		if (debounceRef.current) {
			clearTimeout(debounceRef.current);
		}

		debounceRef.current = setTimeout(async () => {
			if (query.length >= MIN_SEARCH_LENGTH) {
				setSearchState(prev => ({ ...prev, isLoading: true, error: null }));

				try {
					await dispatch(placesAutoComplete(query));
				} catch (error) {
					setSearchState(prev => ({
						...prev,
						error: 'Failed to search locations',
						isLoading: false
					}));
				}
			} else {
				setSearchState(prev => ({
					...prev,
					results: [],
					isLoading: false,
					error: null
				}));
			}
		}, SEARCH_DEBOUNCE_MS);
	}, [dispatch]);

	const updateQuery = useCallback((query: string) => {
		setSearchState(prev => ({ ...prev, query }));
		debouncedSearch(query);
	}, [debouncedSearch]);

	const clearSearch = useCallback(() => {
		if (debounceRef.current) {
			clearTimeout(debounceRef.current);
		}
		setSearchState({
			query: '',
			results: [],
			isLoading: false,
			error: null
		});
	}, []);

	// Update results when Redux places state changes
	useEffect(() => {
		setSearchState(prev => ({
			...prev,
			results: places,
			isLoading: false
		}));
	}, [places]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (debounceRef.current) {
				clearTimeout(debounceRef.current);
			}
		};
	}, []);

	return {
		searchState,
		updateQuery,
		clearSearch,
		isSearching: searchState.isLoading,
		hasResults: searchState.results.length > 0,
		searchError: searchState.error
	};
};
