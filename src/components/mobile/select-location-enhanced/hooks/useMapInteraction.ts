import { useCallback, useRef, useState } from 'react';
import { MapState, LocationRegion } from '../types';

const ANIMATION_DURATION = 1000;
const INTERACTION_THROTTLE_MS = 100;

export const useMapInteraction = () => {
	const animationTimeoutRef = useRef<NodeJS.Timeout>();
	const interactionTimeoutRef = useRef<NodeJS.Timeout>();

	const [mapState, setMapState] = useState<MapState>({
		isMapReady: false,
		isUserInteracting: false,
		animationInProgress: false
	});

	const setMapReady = useCallback(() => {
		setMapState(prev => ({ ...prev, isMapReady: true }));
	}, []);

	const startAnimation = useCallback(() => {
		setMapState(prev => ({ ...prev, animationInProgress: true }));

		if (animationTimeoutRef.current) {
			clearTimeout(animationTimeoutRef.current);
		}

		animationTimeoutRef.current = setTimeout(() => {
			setMapState(prev => ({ ...prev, animationInProgress: false }));
		}, ANIMATION_DURATION);
	}, []);

	const startUserInteraction = useCallback(() => {
		setMapState(prev => ({ ...prev, isUserInteracting: true }));

		if (interactionTimeoutRef.current) {
			clearTimeout(interactionTimeoutRef.current);
		}

		interactionTimeoutRef.current = setTimeout(() => {
			setMapState(prev => ({ ...prev, isUserInteracting: false }));
		}, INTERACTION_THROTTLE_MS);
	}, []);

	const canUpdateRegion = useCallback(() => {
		return mapState.isMapReady && !mapState.animationInProgress;
	}, [mapState.isMapReady, mapState.animationInProgress]);

	const shouldShowLoadingIndicator = useCallback(() => {
		return mapState.isUserInteracting || mapState.animationInProgress;
	}, [mapState.isUserInteracting, mapState.animationInProgress]);

	// Cleanup timeouts on unmount
	const cleanup = useCallback(() => {
		if (animationTimeoutRef.current) {
			clearTimeout(animationTimeoutRef.current);
		}
		if (interactionTimeoutRef.current) {
			clearTimeout(interactionTimeoutRef.current);
		}
	}, []);

	return {
		mapState,
		setMapReady,
		startAnimation,
		startUserInteraction,
		canUpdateRegion,
		shouldShowLoadingIndicator,
		cleanup,
		isMapReady: mapState.isMapReady,
		isUserInteracting: mapState.isUserInteracting,
		animationInProgress: mapState.animationInProgress
	};
};
