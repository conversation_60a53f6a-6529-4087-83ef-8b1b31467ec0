import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import {
	I18nManager,
	StyleSheet,
	Text,
	View,
	TouchableOpacity
} from 'react-native';
import {
	ArrowsRight,
	City,
	Location,
	Region as RegionIcon,
	AlertCircle,
	RefreshCcw
} from '../../../../assets/svgs/icons';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';
import { colors, fonts } from '../../../../utils/theme';
import { PrimaryButton } from '../../../common';
import { AddressDetails } from '../types';
import { locationUtils } from '../utils/locationUtils';

interface LocationInformationEnhancedProps {
	addressDetails: AddressDetails;
	onSubmit: () => void;
	loading?: boolean;
	error?: string | null;
	onRetry?: () => void;
	confirmMode?: boolean;
}

const LocationInformationEnhanced: React.FC<LocationInformationEnhancedProps> = memo(({
	addressDetails,
	onSubmit,
	loading = false,
	error = null,
	onRetry,
	confirmMode = false
}) => {
	const { t } = useTranslation();

	const isCompleteAddress = locationUtils.isCompleteAddress(addressDetails);
	const addressSummary = locationUtils.getAddressSummary(addressDetails);

	if (error) {
		return (
			<View style={styles.container}>
				<View style={styles.errorContainer}>
					<AlertCircle fill={colors.danger} width={24} height={24} />
					<Text style={styles.errorTitle}>{t('location_error')}</Text>
					<Text style={styles.errorMessage}>{error}</Text>
					{onRetry && (
						<TouchableOpacity
							style={styles.retryButton}
							onPress={onRetry}
						>
							<RefreshCcw fill={colors.primary} width={16} height={16} />
							<Text style={styles.retryText}>{t('retry')}</Text>
						</TouchableOpacity>
					)}
				</View>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text style={styles.title}>
				{confirmMode ? t('confirm_location') : t('set_location')}
			</Text>

			{/* Address Information */}
			<View style={styles.addressContainer}>
				<View style={[styles.locationItem, styles.addressTextItem]}>
					<Location fill={colors.primary} width={20} height={20} />
					<Text style={styles.locationItemText} numberOfLines={3}>
						{addressDetails.shippingAddress || t('loading_address')}
					</Text>
				</View>

				{addressDetails.region && (
					<View style={styles.locationItem}>
						<RegionIcon fill={colors.primary} width={20} height={20} />
						<Text style={styles.locationItemText}>
							{addressDetails.region}
						</Text>
					</View>
				)}

				{addressDetails.city && (
					<View style={styles.locationItem}>
						<City fill={colors.primary} width={20} height={20} />
						<Text style={styles.locationItemText}>
							{addressDetails.city}
						</Text>
					</View>
				)}
			</View>

			{/* Address Summary for Confirmation */}
			{confirmMode && (
				<View style={styles.summaryContainer}>
					<Text style={styles.summaryTitle}>{t('selected_location')}</Text>
					<Text style={styles.summaryText}>{addressSummary}</Text>
				</View>
			)}

			{/* Warning for incomplete address */}
			{!isCompleteAddress && !loading && (
				<View style={styles.warningContainer}>
					<AlertCircle fill={colors.warning} width={16} height={16} />
					<Text style={styles.warningText}>
						{t('incomplete_address_warning')}
					</Text>
				</View>
			)}

			{/* Action Button */}
			<PrimaryButton
				title={confirmMode ? t('confirm') : t('next')}
				onPress={onSubmit}
				style={styles.actionButton}
				titleStyle={styles.actionButtonText}
				rightIcon={
					<ArrowsRight
						stroke={colors.white}
						width={16}
						height={16}
						style={{
							transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }]
						}}
					/>
				}
				loading={loading}
				disabled={loading || (!isCompleteAddress && !confirmMode)}
			/>
		</View>
	);
});

LocationInformationEnhanced.displayName = 'LocationInformationEnhanced';

const styles = StyleSheet.create({
	container: {
		paddingVertical: VERTICAL_DIMENS._16
	},
	title: {
		alignSelf: 'center',
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		marginBottom: VERTICAL_DIMENS._20,
		textAlign: 'center'
	},
	addressContainer: {
		marginBottom: VERTICAL_DIMENS._16
	},
	locationItem: {
		alignItems: 'flex-start',
		flexDirection: 'row',
		paddingVertical: VERTICAL_DIMENS._12,
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	addressTextItem: {
		alignItems: 'flex-start'
	},
	locationItemText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._16,
		marginLeft: HORIZONTAL_DIMENS._12,
		flex: 1,
		textAlign: I18nManager.isRTL ? 'right' : 'left'
	},
	summaryContainer: {
		backgroundColor: colors.grey100,
		borderRadius: 8,
		padding: HORIZONTAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._16
	},
	summaryTitle: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._14,
		marginBottom: VERTICAL_DIMENS._8
	},
	summaryText: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		lineHeight: 20
	},
	warningContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		backgroundColor: colors.warningLight,
		borderRadius: 8,
		padding: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._16
	},
	warningText: {
		color: colors.warning,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginLeft: HORIZONTAL_DIMENS._8,
		flex: 1
	},
	errorContainer: {
		alignItems: 'center',
		padding: HORIZONTAL_DIMENS._20
	},
	errorTitle: {
		color: colors.danger,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._8,
		marginBottom: VERTICAL_DIMENS._4
	},
	errorMessage: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center',
		marginBottom: VERTICAL_DIMENS._16
	},
	retryButton: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._8,
		borderRadius: 20,
		borderWidth: 1,
		borderColor: colors.primary
	},
	retryText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		marginLeft: HORIZONTAL_DIMENS._8
	},
	actionButton: {
		marginTop: VERTICAL_DIMENS._20,
		marginHorizontal: HORIZONTAL_DIMENS._24
	},
	actionButtonText: {
		textTransform: 'uppercase'
	}
});

export { LocationInformationEnhanced };
