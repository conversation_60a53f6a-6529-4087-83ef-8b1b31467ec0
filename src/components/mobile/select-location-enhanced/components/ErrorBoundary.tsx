import React, { Component, ReactNode } from 'react';
import {
	StyleSheet,
	View,
	Text,
	TouchableOpacity
} from 'react-native';
import { colors, fonts } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';
import { AlertCircle, RefreshCcw } from '../../../../assets/svgs/icons';

interface ErrorBoundaryProps {
	children: ReactNode;
	fallback?: ReactNode;
	onError?: (error: Error, errorInfo: any) => void;
}

interface ErrorBoundaryState {
	hasError: boolean;
	error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = {
			hasError: false,
			error: null
		};
	}

	static getDerivedStateFromError(error: Error): ErrorBoundaryState {
		return {
			hasError: true,
			error
		};
	}

	componentDidCatch(error: Error, errorInfo: any) {
		console.error('LocationSelector Error:', error, errorInfo);
		this.props.onError?.(error, errorInfo);
	}

	handleRetry = () => {
		this.setState({
			hasError: false,
			error: null
		});
	};

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				return this.props.fallback;
			}

			return (
				<View style={styles.container}>
					<View style={styles.errorContainer}>
						<AlertCircle fill={colors.danger} width={48} height={48} />
						<Text style={styles.title}>Something went wrong</Text>
						<Text style={styles.message}>
							An error occurred while loading the location selector. Please try again.
						</Text>
						<TouchableOpacity
							style={styles.retryButton}
							onPress={this.handleRetry}
						>
							<RefreshCcw fill={colors.white} width={16} height={16} />
							<Text style={styles.retryText}>Try Again</Text>
						</TouchableOpacity>
					</View>
				</View>
			);
		}

		return this.props.children;
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: colors.white,
		justifyContent: 'center',
		alignItems: 'center'
	},
	errorContainer: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._32,
		maxWidth: 300
	},
	title: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._20,
		fontWeight: '600',
		marginTop: VERTICAL_DIMENS._16,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	message: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center',
		lineHeight: 20,
		marginBottom: VERTICAL_DIMENS._24
	},
	retryButton: {
		backgroundColor: colors.primary,
		flexDirection: 'row',
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._24,
		paddingVertical: VERTICAL_DIMENS._12,
		borderRadius: 8
	},
	retryText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '500',
		marginLeft: HORIZONTAL_DIMENS._8
	}
});

export { ErrorBoundary };
