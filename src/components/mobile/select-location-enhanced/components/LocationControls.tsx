import React, { memo } from 'react';
import { StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { LocationGPS } from '../../../../assets/svgs/icons';
import { colors } from '../../../../utils/theme';

interface LocationControlsProps {
	bottom: number;
	onUserLocationPress: () => void;
	isLoading?: boolean;
}

const LocationControls: React.FC<LocationControlsProps> = memo(({
	bottom,
	onUserLocationPress,
	isLoading = false
}) => {
	const { t } = useTranslation();

	return (
		<TouchableOpacity
			style={[styles.locationButton, { bottom }]}
			onPress={onUserLocationPress}
			disabled={isLoading}
			accessibilityLabel={t('get_current_location')}
			accessibilityHint={t('tap_to_center_map_on_current_location')}
			accessibilityRole="button"
		>
			{isLoading ? (
				<ActivityIndicator
					size="small"
					color={colors.primary}
				/>
			) : (
				<LocationGPS
					fill={colors.primary}
					width={24}
					height={24}
				/>
			)}
		</TouchableOpacity>
	);
});

LocationControls.displayName = 'LocationControls';

const styles = StyleSheet.create({
	locationButton: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 30,
		justifyContent: 'center',
		position: 'absolute',
		right: 16,
		height: 54,
		width: 54,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 2
		},
		shadowOpacity: 0.14,
		shadowRadius: 4,
		elevation: 4
	}
});

export { LocationControls };
