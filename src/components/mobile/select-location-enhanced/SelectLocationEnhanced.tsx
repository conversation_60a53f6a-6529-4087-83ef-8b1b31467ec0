import React, { useCallback, useRef, useState, useEffect } from 'react';
import {
	Alert,
	InteractionManager,
	Keyboard,
	LayoutChangeEvent,
	Platform,
	StatusBar,
	StyleSheet,
	View
} from 'react-native';
import { useDispatch } from 'react-redux';
import Modal from 'react-native-modal';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Components
import { LocationHeader } from './components/LocationHeader';
import { LocationMarker } from './components/LocationMarker';
import { LocationControls } from './components/LocationControls';
import { LocationInformationEnhanced } from './components/LocationInformationEnhanced';
import { ShippingNumberEnhanced } from './components/ShippingNumberEnhanced';
import { LoadingOverlay } from './components/LoadingOverlay';
import { ErrorBoundary } from './components/ErrorBoundary';

// Hooks
import { useLocationManager } from './hooks/useLocationManager';
import { useLocationSearch } from './hooks/useLocationSearch';
import { useMapInteraction } from './hooks/useMapInteraction';

// Utils and Types
import { locationUtils } from './utils/locationUtils';
import {
	SelectLocationProps,
	LocationStepEnum,
	LocationSelectionData
} from './types';

// Redux
import { useAppSelector } from '../../../redux/hooks';
import { getTenantCountry } from '../../../redux/selectors';

import { updateCustomerTenantShippingAddress } from '../../../redux/apis/customer';

// Constants and Utils

import { colors } from '../../../utils/theme';
import { checkBadRequest } from '../../../utils/functions';
import useStatusBarHeight from '../../../hooks/useStatusbarHeight';

const SelectLocationEnhanced: React.FC<SelectLocationProps> = ({
	isVisible,
	onClose,
	onSelect,
	customerRoleId,
	shippingNumber,
	initialLocation,
	title,
	showShippingStep = true
}) => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const { bottom } = useSafeAreaInsets();
	const statusBarHeight = useStatusBarHeight();

	// Redux selectors
	const tenantCountry = useAppSelector(getTenantCountry);

	// Refs
	const mapRef = useRef<MapView | null>(null);
	const bottomSheetRef = useRef<BottomSheet>(null);

	// Custom hooks
	const {
		updateRegion,
		getCurrentUserLocation,
		animateToLocation,
		clearError,
		isLoading: locationLoading,
		errorMessage: locationError,
		region,
		addressDetails
	} = useLocationManager(initialLocation);

	const {
		searchState,
		updateQuery,
		clearSearch,
		isSearching,
		hasResults,
		searchError
	} = useLocationSearch();

	const {
		setMapReady,
		startAnimation,
		startUserInteraction,
		canUpdateRegion,
		shouldShowLoadingIndicator,
		cleanup: cleanupMapInteraction
	} = useMapInteraction();

	// Local state
	const [currentStep, setCurrentStep] = useState<LocationStepEnum>(LocationStepEnum.MAP_SELECTION);
	const [bottomSheetHeight, setBottomSheetHeight] = useState<number>(0);
	const [savingLocation, setSavingLocation] = useState(false);
	const [isAnimatingToLocation, setIsAnimatingToLocation] = useState(false);
	const [keyboardHeight, setKeyboardHeight] = useState(0);

	// Keyboard listeners for better modal handling
	useEffect(() => {
		const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
			setKeyboardHeight(e.endCoordinates.height);
		});

		const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
			setKeyboardHeight(0);
		});

		return () => {
			keyboardDidShowListener.remove();
			keyboardDidHideListener.remove();
		};
	}, []);

	// Handle map region changes
	const handleRegionChange = useCallback((newRegion: Region) => {
		if (canUpdateRegion()) {
			updateRegion(newRegion);
		}
	}, [canUpdateRegion, updateRegion]);

	// Handle map region change start
	const handleRegionChangeStart = useCallback(() => {
		startUserInteraction();
	}, [startUserInteraction]);

	// Handle user location button press
	const handleUserLocationPress = useCallback(async () => {
		if (isAnimatingToLocation || locationLoading) return;

		setIsAnimatingToLocation(true);
		try {
			const userLocation = await getCurrentUserLocation();
			if (userLocation && mapRef.current) {
				startAnimation();
				const newRegion = animateToLocation(userLocation);
				mapRef.current.animateToRegion(newRegion, 1000);

				// Reset animation state after animation completes
				setTimeout(() => {
					setIsAnimatingToLocation(false);
				}, 1200);
			} else {
				setIsAnimatingToLocation(false);
			}
		} catch {
			setIsAnimatingToLocation(false);
		}
	}, [getCurrentUserLocation, animateToLocation, startAnimation, isAnimatingToLocation, locationLoading]);

	// Handle place selection from search
	const handlePlaceSelect = useCallback((location: { lat: number; lng: number }) => {
		if (isAnimatingToLocation || locationLoading) return;

		if (mapRef.current) {
			setIsAnimatingToLocation(true);
			startAnimation();
			const newRegion = animateToLocation({
				latitude: location.lat,
				longitude: location.lng
			});
			mapRef.current.animateToRegion(newRegion, 1000);
			clearSearch();

			// Reset animation state after animation completes
			setTimeout(() => {
				setIsAnimatingToLocation(false);
			}, 1200);
		}
	}, [animateToLocation, startAnimation, clearSearch, isAnimatingToLocation, locationLoading]);

	// Handle bottom sheet layout change
	const handleBottomSheetLayout = useCallback((event: LayoutChangeEvent) => {
		if (currentStep === LocationStepEnum.MAP_SELECTION) {
			setBottomSheetHeight(event.nativeEvent.layout.height + 24);
		}
	}, [currentStep]);

	return (
		<ErrorBoundary>
			<Modal
				isVisible={isVisible}
				style={styles.modalContainer}
				onModalShow={() => {
					if (!initialLocation) {
						getCurrentUserLocation();
					}
				}}
				onModalHide={cleanupMapInteraction}
			>
				<View style={styles.modalContent}>
					<StatusBar
						barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
					/>

					{/* Map View */}
					<MapView
						provider={PROVIDER_GOOGLE}
						ref={mapRef}
						style={styles.map}
						initialRegion={region}
						rotateEnabled={false}
						pitchEnabled={false}
						showsUserLocation={true}
						showsMyLocationButton={false}
						showsIndoors={false}
						onRegionChangeComplete={handleRegionChange}
						onRegionChangeStart={handleRegionChangeStart}
						onMapReady={setMapReady}
						mapPadding={{
							top: 0,
							right: 0,
							bottom: bottomSheetHeight - 22,
							left: 0
						}}
						scrollEnabled={currentStep === LocationStepEnum.MAP_SELECTION}
						zoomControlEnabled={currentStep === LocationStepEnum.MAP_SELECTION}
						zoomEnabled={currentStep === LocationStepEnum.MAP_SELECTION}
						zoomTapEnabled={currentStep === LocationStepEnum.MAP_SELECTION}
						scrollDuringRotateOrZoomEnabled={currentStep === LocationStepEnum.MAP_SELECTION}
					/>

					{/* Location Marker */}
					<LocationMarker
						bottom={bottomSheetHeight}
						isLoading={shouldShowLoadingIndicator() || locationLoading || isAnimatingToLocation}
					/>

					{/* User Location Button */}
					{currentStep === LocationStepEnum.MAP_SELECTION && (
						<LocationControls
							bottom={bottomSheetHeight + 16}
							onUserLocationPress={handleUserLocationPress}
							isLoading={locationLoading || isAnimatingToLocation}
						/>
					)}

					{/* Header with Search */}
					<LocationHeader
						title={title || t('select_location')}
						searchQuery={searchState.query}
						onSearchChange={updateQuery}
						onClearSearch={clearSearch}
						onBackPress={() => {
							if (currentStep === LocationStepEnum.LOCATION_CONFIRMATION) {
								setCurrentStep(LocationStepEnum.MAP_SELECTION);
							} else {
								onClose();
							}
						}}
						searchResults={searchState.results}
						onPlaceSelect={handlePlaceSelect}
						isSearching={isSearching}
						hasSearchResults={hasResults}
						searchError={searchError}
						statusBarHeight={statusBarHeight}
						disabled={savingLocation || isAnimatingToLocation}
						editable={currentStep === LocationStepEnum.MAP_SELECTION}
					/>

					{/* Loading Overlay */}
					{(locationLoading || savingLocation || isAnimatingToLocation) && (
						<LoadingOverlay
							message={
								savingLocation
									? t('saving_location')
									: isAnimatingToLocation
										? t('locating')
										: t('loading_location')
							}
						/>
					)}

					{/* Bottom Sheet */}
					<BottomSheet
						ref={bottomSheetRef}
						index={0}
						animateOnMount={true}
						handleIndicatorStyle={styles.bottomSheetIndicator}
						keyboardBlurBehavior="restore"
						keyboardBehavior="interactive"
						android_keyboardInputMode="adjustResize"
						enableDynamicSizing={true}
						enablePanDownToClose={false}
					>
						<BottomSheetView
							onLayout={handleBottomSheetLayout}
							style={[
								styles.bottomSheet,
								bottom > 0 && { paddingBottom: bottom },
								Platform.OS === 'android' && {
									paddingBottom: keyboardHeight > 0 ? 10 : 20
								},
								currentStep === LocationStepEnum.SHIPPING_DETAILS && keyboardHeight > 0 && {
									paddingBottom: Platform.OS === 'android' ? 10 : bottom
								}
							]}
						>
							{currentStep === LocationStepEnum.MAP_SELECTION && (
								<LocationInformationEnhanced
									addressDetails={addressDetails}
									onSubmit={handleLocationConfirm}
									loading={locationLoading}
									error={locationError}
									onRetry={clearError}
								/>
							)}

							{currentStep === LocationStepEnum.LOCATION_CONFIRMATION && (
								<LocationInformationEnhanced
									addressDetails={addressDetails}
									onSubmit={handleNext}
									loading={false}
									confirmMode={true}
								/>
							)}

							{currentStep === LocationStepEnum.SHIPPING_DETAILS && showShippingStep && (
								<ShippingNumberEnhanced
									loading={savingLocation}
									onSubmit={handleSubmitShipping}
									shippingNumber={shippingNumber}
									onBack={() => setCurrentStep(LocationStepEnum.LOCATION_CONFIRMATION)}
								/>
							)}
						</BottomSheetView>
					</BottomSheet>
				</View>
			</Modal>
		</ErrorBoundary>
	);

	// Handle location confirmation
	async function handleLocationConfirm() {
		if (!locationUtils.isCompleteAddress(addressDetails)) {
			Alert.alert(t('incomplete_address'), t('please_select_valid_location'));
			return;
		}

		setCurrentStep(LocationStepEnum.LOCATION_CONFIRMATION);
	}

	// Handle next step
	async function handleNext() {
		if (showShippingStep) {
			setCurrentStep(LocationStepEnum.SHIPPING_DETAILS);
		} else {
			// Skip shipping step and submit directly
			handleSubmitLocation();
		}
	}

	// Handle shipping number submission
	async function handleSubmitShipping(
		shippingMobileNumber: string,
		shippingCountryCode: string
	) {
		await handleSubmitLocation(shippingMobileNumber, shippingCountryCode);
	}

	// Handle final location submission
	async function handleSubmitLocation(
		shippingMobileNumber?: string,
		shippingCountryCode?: string
	) {
		try {
			setSavingLocation(true);

			// Cache the location for future use
			await locationUtils.cacheLocation(
				{ latitude: region.latitude, longitude: region.longitude },
				addressDetails
			);

			// Save to recent locations
			await locationUtils.saveToRecentLocations(
				{ latitude: region.latitude, longitude: region.longitude },
				addressDetails
			);

			if (customerRoleId) {
				// Save location to database
				const success = await saveLocationToDatabase(
					shippingMobileNumber,
					shippingCountryCode
				);

				if (!success) {
					setSavingLocation(false);
					return;
				}
			}

			// Prepare selection data
			const selectionData: LocationSelectionData = {
				...addressDetails,
				latitude: region.latitude,
				longitude: region.longitude,
				...(shippingMobileNumber && { shippingMobileNumber }),
				...(shippingCountryCode && { shippingCountryCode })
			};

			onSelect(selectionData);

			InteractionManager.runAfterInteractions(() => {
				setCurrentStep(LocationStepEnum.MAP_SELECTION);
				setSavingLocation(false);
			});

		} catch (error) {
			console.error('Error submitting location:', error);
			setSavingLocation(false);
			Alert.alert(t('error'), t('failed_to_save_location'));
		}
	}

	// Save location to database
	async function saveLocationToDatabase(
		shippingMobileNumber?: string,
		shippingCountryCode?: string
	): Promise<boolean> {
		try {
			let number = shippingMobileNumber || '';

			// Remove leading zero if present
			if (number.startsWith('0')) {
				number = number.substring(1);
			}

			const requestBody = {
				ids: [customerRoleId],
				updateFields: {
					shipping_address: addressDetails.shippingAddress,
					shipping_country_id: tenantCountry._id,
					shipping_city_id: addressDetails.shippingCityId,
					shipping_region_id: addressDetails.shippingRegionId,
					...(shippingCountryCode && { shipping_country_code: shippingCountryCode }),
					...(number && { shipping_mobile_number: number }),
					gps_coordinates: {
						latitude: region.latitude,
						longitude: region.longitude
					}
				}
			};

			const response = await dispatch(updateCustomerTenantShippingAddress(requestBody));

			if (response.error) {
				Alert.alert(t(checkBadRequest(response.payload)));
				return false;
			}

			return true;
		} catch (error) {
			console.error('Database save error:', error);
			Alert.alert(t('error'), t('failed_to_save_location'));
			return false;
		}
	}


};

const styles = StyleSheet.create({
	modalContainer: {
		justifyContent: 'flex-end',
		margin: 0
	},
	modalContent: {
		backgroundColor: colors.grey100,
		flex: 1
	},
	map: {
		flex: 1
	},
	bottomSheet: {
		paddingHorizontal: 16
	},
	bottomSheetIndicator: {
		backgroundColor: colors.grey300
	}
});

export { SelectLocationEnhanced };
