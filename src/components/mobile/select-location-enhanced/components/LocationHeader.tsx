import React, { memo } from 'react';
import {
	I18nManager,
	Platform,
	StyleSheet,
	Text,
	TextInput,
	TouchableOpacity,
	View
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, CloseCircle, Search } from '../../../../assets/svgs/icons';
import { PlacesSearchResultsEnhanced } from './PlacesSearchResultsEnhanced';
import { colors, fonts, headerHeightMobile } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS } from '../../../../constants';
import { PlaceResult } from '../types';

interface LocationHeaderProps {
	title: string;
	searchQuery: string;
	onSearchChange: (query: string) => void;
	onClearSearch: () => void;
	onBackPress: () => void;
	searchResults: PlaceResult[];
	onPlaceSelect: (location: { lat: number; lng: number }) => void;
	isSearching: boolean;
	hasSearchResults: boolean;
	searchError: string | null;
	statusBarHeight: number;
	disabled?: boolean;
	editable?: boolean;
}

const LocationHeader: React.FC<LocationHeaderProps> = memo(({
	title,
	searchQuery,
	onSearchChange,
	onClearSearch,
	onBackPress,
	searchResults,
	onPlaceSelect,
	isSearching,
	hasSearchResults,
	searchError,
	statusBarHeight,
	disabled = false,
	editable = true
}) => {
	const { t } = useTranslation();

	return (
		<View
			style={[
				styles.container,
				Platform.OS === 'ios' && { top: statusBarHeight }
			]}
		>
			<TouchableOpacity
				onPress={onBackPress}
				disabled={disabled}
				style={styles.backButton}
				accessibilityLabel={t('go_back')}
				accessibilityRole="button"
			>
				<ArrowLeft
					fill={colors.primary}
					style={{
						transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
						opacity: disabled ? 0.5 : 1
					}}
				/>
			</TouchableOpacity>

			<View style={styles.searchContainer}>
				<View style={styles.searchInputContainer}>
					<TextInput
						style={[
							styles.searchInput,
							!editable && styles.searchInputDisabled
						]}
						placeholder={t('search_place')}
						placeholderTextColor={colors.grey400}
						value={searchQuery}
						onChangeText={onSearchChange}
						editable={editable && !disabled}
						textAlign={I18nManager.isRTL ? 'right' : 'left'}
						accessibilityLabel={t('search_location_input')}
						accessibilityHint={t('type_to_search_locations')}
					/>

					{searchQuery.length > 0 && (
						<TouchableOpacity
							style={styles.clearButton}
							onPress={onClearSearch}
							disabled={disabled}
							accessibilityLabel={t('clear_search')}
							accessibilityRole="button"
						>
							<CloseCircle
								fill={disabled ? colors.grey400 : colors.grey600}
							/>
						</TouchableOpacity>
					)}

					<View style={styles.searchIcon}>
						<Search fill={colors.grey600} />
					</View>
				</View>

				{/* Search Results */}
				{editable && (hasSearchResults || isSearching || searchError) && (
					<PlacesSearchResultsEnhanced
						results={searchResults}
						onSelect={onPlaceSelect}
						isLoading={isSearching}
						error={searchError}
						query={searchQuery}
					/>
				)}
			</View>
		</View>
	);
});

LocationHeader.displayName = 'LocationHeader';

const styles = StyleSheet.create({
	container: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		flexDirection: 'row',
		width: '100%',
		height: headerHeightMobile,
		position: 'absolute',
		backgroundColor: 'transparent',
		zIndex: 1000
	},
	backButton: {
		padding: 8,
		marginRight: HORIZONTAL_DIMENS._8
	},
	searchContainer: {
		flex: 1,
		position: 'relative'
	},
	searchInputContainer: {
		position: 'relative'
	},
	searchInput: {
		backgroundColor: colors.white,
		borderRadius: 20,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		height: 40,
		paddingLeft: HORIZONTAL_DIMENS._48,
		paddingRight: HORIZONTAL_DIMENS._48,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 2
		},
		shadowOpacity: 0.1,
		shadowRadius: 4,
		elevation: 3
	},
	searchInputDisabled: {
		backgroundColor: colors.grey200,
		color: colors.grey500
	},
	clearButton: {
		position: 'absolute',
		right: HORIZONTAL_DIMENS._12,
		top: 0,
		bottom: 0,
		justifyContent: 'center',
		paddingHorizontal: 4
	},
	searchIcon: {
		position: 'absolute',
		left: HORIZONTAL_DIMENS._16,
		top: 0,
		bottom: 0,
		justifyContent: 'center'
	}
});

export { LocationHeader };
