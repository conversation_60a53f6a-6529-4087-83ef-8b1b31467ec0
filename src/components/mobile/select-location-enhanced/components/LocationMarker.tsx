import React, { memo } from 'react';
import { StyleSheet, View, Animated } from 'react-native';
import { LocationPin } from '../../../../assets/svgs/icons';
import { colors } from '../../../../utils/theme';

interface LocationMarkerProps {
	bottom: number;
	isLoading?: boolean;
}

const LocationMarker: React.FC<LocationMarkerProps> = memo(({
	bottom,
	isLoading = false
}) => {
	const animatedValue = React.useRef(new Animated.Value(1)).current;

	React.useEffect(() => {
		if (isLoading) {
			// Create a pulsing animation when loading
			const pulseAnimation = Animated.loop(
				Animated.sequence([
					Animated.timing(animatedValue, {
						toValue: 0.8,
						duration: 600,
						useNativeDriver: true
					}),
					Animated.timing(animatedValue, {
						toValue: 1,
						duration: 600,
						useNativeDriver: true
					})
				])
			);

			pulseAnimation.start();

			return () => {
				pulseAnimation.stop();
			};
		} else {
			// Reset to normal scale when not loading
			Animated.timing(animatedValue, {
				toValue: 1,
				duration: 200,
				useNativeDriver: true
			}).start();
		}
	}, [isLoading, animatedValue]);

	return (
		<View
			style={[styles.container, { bottom }]}
			pointerEvents="none"
		>
			<Animated.View
				style={[
					styles.markerContainer,
					{
						transform: [{ scale: animatedValue }]
					}
				]}
			>
				<LocationPin
					fill={isLoading ? colors.secondary : colors.primary}
					width={32}
					height={32}
				/>
			</Animated.View>

			{/* Shadow/Drop effect */}
			<View style={styles.shadow} />
		</View>
	);
});

LocationMarker.displayName = 'LocationMarker';

const styles = StyleSheet.create({
	container: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'transparent',
		zIndex: 100
	},
	markerContainer: {
		alignItems: 'center',
		justifyContent: 'center'
	},
	shadow: {
		width: 20,
		height: 8,
		backgroundColor: colors.black,
		opacity: 0.2,
		borderRadius: 10,
		marginTop: 4
	}
});

export { LocationMarker };
