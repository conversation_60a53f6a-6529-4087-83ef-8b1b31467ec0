import { useCallback, useEffect, useState } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Permission } from 'react-native';

interface LocationPermissionState {
	hasPermission: boolean;
	isLoading: boolean;
	error: string | null;
	permissionStatus: 'granted' | 'denied' | 'blocked' | 'unknown';
}

export const useLocationPermissions = () => {
	const { t } = useTranslation();

	const [permissionState, setPermissionState] = useState<LocationPermissionState>({
		hasPermission: false,
		isLoading: false,
		error: null,
		permissionStatus: 'unknown'
	});

	const openSettings = useCallback(() => {
		Linking.openSettings().catch(() => {
			Alert.alert(t('unable_open_setting'));
		});
	}, [t]);

	const checkIOSPermission = useCallback(async (): Promise<boolean> => {
		try {
			const status = await Geolocation.requestAuthorization('whenInUse');

			setPermissionState(prev => ({
				...prev,
				permissionStatus: status as any,
				hasPermission: status === 'granted'
			}));

			if (status === 'granted') {
				return true;
			}

			if (status === 'denied') {
				setPermissionState(prev => ({
					...prev,
					error: t('location_permission_denied')
				}));
				Alert.alert(t('location_permission_denied'));
			}

			if (status === 'disabled') {
				Alert.alert(
					t('turn_on_location'),
					'',
					[
						{ text: t('go_to_settings'), onPress: openSettings },
						{ text: t('dont_use_location'), onPress: () => { } }
					]
				);
			}

			return false;
		} catch (error) {
			console.error('iOS permission check error:', error);
			setPermissionState(prev => ({
				...prev,
				error: t('permission_check_failed'),
				hasPermission: false
			}));
			return false;
		}
	}, [t, openSettings]);

	const checkAndroidPermission = useCallback(async (): Promise<boolean> => {
		try {
			if (Platform.Version < 23) {
				setPermissionState(prev => ({
					...prev,
					hasPermission: true,
					permissionStatus: 'granted'
				}));
				return true;
			}

			let permission: Permission;
			if (Number(Platform.Version) < 29) {
				permission = PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION;
			} else {
				permission = PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION;
			}

			const hasPermission = await PermissionsAndroid.check(permission);

			if (hasPermission) {
				setPermissionState(prev => ({
					...prev,
					hasPermission: true,
					permissionStatus: 'granted'
				}));
				return true;
			}

			const status = await PermissionsAndroid.request(permission, {
				title: t('location_permission_title'),
				message: t('location_permission_message'),
				buttonNeutral: t('ask_me_later'),
				buttonNegative: t('cancel'),
				buttonPositive: t('ok')
			});

			const granted = status === PermissionsAndroid.RESULTS.GRANTED;

			setPermissionState(prev => ({
				...prev,
				hasPermission: granted,
				permissionStatus: granted ? 'granted' : 'denied'
			}));

			if (!granted) {
				setPermissionState(prev => ({
					...prev,
					error: t('location_permission_required')
				}));
			}

			return granted;
		} catch (error) {
			console.error('Android permission check error:', error);
			setPermissionState(prev => ({
				...prev,
				error: t('permission_check_failed'),
				hasPermission: false
			}));
			return false;
		}
	}, [t]);

	const requestLocationPermission = useCallback(async (): Promise<boolean> => {
		setPermissionState(prev => ({
			...prev,
			isLoading: true,
			error: null
		}));

		try {
			const hasPermission = Platform.OS === 'ios'
				? await checkIOSPermission()
				: await checkAndroidPermission();

			setPermissionState(prev => ({
				...prev,
				isLoading: false,
				hasPermission
			}));

			return hasPermission;
		} catch (error) {
			console.error('Permission request error:', error);
			setPermissionState(prev => ({
				...prev,
				isLoading: false,
				error: t('permission_request_failed'),
				hasPermission: false
			}));
			return false;
		}
	}, [checkIOSPermission, checkAndroidPermission, t]);

	const checkCurrentPermission = useCallback(async () => {
		setPermissionState(prev => ({ ...prev, isLoading: true }));

		try {
			if (Platform.OS === 'ios') {
				// For iOS, we need to request to check current status
				await checkIOSPermission();
			} else {
				// For Android, we can check without requesting
				const permission = PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION;
				const hasPermission = await PermissionsAndroid.check(permission);

				setPermissionState(prev => ({
					...prev,
					hasPermission,
					permissionStatus: hasPermission ? 'granted' : 'denied',
					isLoading: false
				}));
			}
		} catch (error) {
			console.error('Check permission error:', error);
			setPermissionState(prev => ({
				...prev,
				isLoading: false,
				error: t('permission_check_failed')
			}));
		}
	}, [checkIOSPermission, t]);

	// Check permission on mount
	useEffect(() => {
		checkCurrentPermission();
	}, [checkCurrentPermission]);

	return {
		...permissionState,
		requestLocationPermission,
		checkCurrentPermission,
		openSettings
	};
};
