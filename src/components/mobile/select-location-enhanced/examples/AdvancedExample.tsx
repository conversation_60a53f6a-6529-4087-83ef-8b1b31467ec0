import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Alert } from 'react-native';
import { SelectLocationEnhanced } from '../SelectLocationEnhanced';
import { LocationSelectionData, LocationCoordinates } from '../types';
import { locationUtils } from '../utils/locationUtils';
import { getCurrentLocation } from '../../../../utils/functions';
import { colors, fonts } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';

/**
 * Advanced example showing all features of SelectLocationEnhanced
 */
const AdvancedExample: React.FC = () => {
	const [isVisible, setIsVisible] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<LocationSelectionData | null>(null);
	const [initialLocation, setInitialLocation] = useState<LocationCoordinates | undefined>();
	const [customerRoleId] = useState('customer_123'); // Simulate customer ID
	const [existingShippingNumber] = useState('+1234567890'); // Simulate existing number

	// Get user's current location on component mount
	useEffect(() => {
		getCurrentUserLocation();
	}, []);

	const getCurrentUserLocation = async () => {
		try {
			const location: any = await getCurrentLocation();
			if (location?.coords) {
				setInitialLocation({
					latitude: location.coords.latitude,
					longitude: location.coords.longitude
				});
			}
		} catch (error) {
			console.log('Could not get current location:', error);
			// Use default location (San Francisco)
			setInitialLocation({
				latitude: 37.78825,
				longitude: -122.4324
			});
		}
	};

	const handleLocationSelect = async (locationData: LocationSelectionData) => {
		console.log('Advanced location selection:', {
			address: locationData.shippingAddress,
			coordinates: {
				lat: locationData.latitude,
				lng: locationData.longitude
			},
			city: locationData.city,
			region: locationData.region,
			country: locationData.country,
			phone: locationData.shippingMobileNumber,
			countryCode: locationData.shippingCountryCode,
			cityId: locationData.shippingCityId,
			regionId: locationData.shippingRegionId
		});

		setSelectedLocation(locationData);
		setIsVisible(false);

		// Show success message
		Alert.alert(
			'Location Saved',
			`Successfully saved location: ${locationUtils.getAddressSummary(locationData)}`,
			[{ text: 'OK' }]
		);
	};

	const handleOpenLocationSelector = () => {
		setIsVisible(true);
	};

	const clearSelectedLocation = () => {
		setSelectedLocation(null);
	};

	const showLocationDetails = () => {
		if (!selectedLocation) return;

		const details = [
			`Address: ${selectedLocation.shippingAddress}`,
			`City: ${selectedLocation.city}`,
			`Region: ${selectedLocation.region}`,
			`Country: ${selectedLocation.country}`,
			`Coordinates: ${selectedLocation.latitude.toFixed(6)}, ${selectedLocation.longitude.toFixed(6)}`,
			selectedLocation.shippingMobileNumber ? `Phone: ${selectedLocation.shippingCountryCode} ${selectedLocation.shippingMobileNumber}` : '',
			`City ID: ${selectedLocation.shippingCityId}`,
			`Region ID: ${selectedLocation.shippingRegionId}`
		].filter(Boolean).join('\n');

		Alert.alert('Location Details', details, [{ text: 'OK' }]);
	};

	return (
		<View style={styles.container}>
			<Text style={styles.title}>Advanced Location Selector</Text>
			<Text style={styles.subtitle}>
				This example demonstrates all features including customer saving,
				shipping number collection, and initial location setting.
			</Text>

			<TouchableOpacity
				style={styles.primaryButton}
				onPress={handleOpenLocationSelector}
			>
				<Text style={styles.primaryButtonText}>
					{selectedLocation ? 'Change Location' : 'Select Location'}
				</Text>
			</TouchableOpacity>

			{selectedLocation && (
				<View style={styles.selectedLocationContainer}>
					<Text style={styles.selectedLocationTitle}>Selected Location</Text>

					<View style={styles.locationInfo}>
						<Text style={styles.locationAddress}>
							{selectedLocation.shippingAddress}
						</Text>
						<Text style={styles.locationDetails}>
							{selectedLocation.city}, {selectedLocation.region}
						</Text>
						{selectedLocation.shippingMobileNumber && (
							<Text style={styles.locationPhone}>
								📞 {selectedLocation.shippingCountryCode} {selectedLocation.shippingMobileNumber}
							</Text>
						)}
						<Text style={styles.locationCoords}>
							📍 {selectedLocation.latitude.toFixed(6)}, {selectedLocation.longitude.toFixed(6)}
						</Text>
					</View>

					<View style={styles.actionButtons}>
						<TouchableOpacity
							style={styles.secondaryButton}
							onPress={showLocationDetails}
						>
							<Text style={styles.secondaryButtonText}>View Details</Text>
						</TouchableOpacity>

						<TouchableOpacity
							style={styles.clearButton}
							onPress={clearSelectedLocation}
						>
							<Text style={styles.clearButtonText}>Clear</Text>
						</TouchableOpacity>
					</View>
				</View>
			)}

			<SelectLocationEnhanced
				isVisible={isVisible}
				onClose={() => setIsVisible(false)}
				onSelect={handleLocationSelect}
				customerRoleId={customerRoleId} // This will save to database
				shippingNumber={existingShippingNumber} // Pre-fill shipping number
				initialLocation={initialLocation} // Start at user's location
				title="Select Delivery Location"
				showShippingStep={true} // Include shipping number step
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: HORIZONTAL_DIMENS._20,
		backgroundColor: colors.white
	},
	title: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Bold,
		fontSize: HORIZONTAL_DIMENS._24,
		fontWeight: '700',
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	subtitle: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		lineHeight: 20,
		textAlign: 'center',
		marginBottom: VERTICAL_DIMENS._32
	},
	primaryButton: {
		backgroundColor: colors.primary,
		paddingHorizontal: HORIZONTAL_DIMENS._24,
		paddingVertical: VERTICAL_DIMENS._16,
		borderRadius: 8,
		alignItems: 'center',
		marginBottom: VERTICAL_DIMENS._24
	},
	primaryButtonText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '600'
	},
	selectedLocationContainer: {
		backgroundColor: colors.grey100,
		borderRadius: 12,
		padding: HORIZONTAL_DIMENS._16,
		borderWidth: 1,
		borderColor: colors.grey200
	},
	selectedLocationTitle: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._18,
		fontWeight: '600',
		marginBottom: VERTICAL_DIMENS._12
	},
	locationInfo: {
		marginBottom: VERTICAL_DIMENS._16
	},
	locationAddress: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '500',
		marginBottom: VERTICAL_DIMENS._4
	},
	locationDetails: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		marginBottom: VERTICAL_DIMENS._4
	},
	locationPhone: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		marginBottom: VERTICAL_DIMENS._4
	},
	locationCoords: {
		color: colors.grey500,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12
	},
	actionButtons: {
		flexDirection: 'row',
		justifyContent: 'space-between'
	},
	secondaryButton: {
		backgroundColor: colors.secondary,
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._8,
		borderRadius: 6,
		flex: 1,
		marginRight: HORIZONTAL_DIMENS._8,
		alignItems: 'center'
	},
	secondaryButtonText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '500'
	},
	clearButton: {
		backgroundColor: colors.danger,
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._8,
		borderRadius: 6,
		flex: 1,
		marginLeft: HORIZONTAL_DIMENS._8,
		alignItems: 'center'
	},
	clearButtonText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '500'
	}
});

export default AdvancedExample;
