import { useCallback, useEffect, useState } from 'react';
import { locationUtils } from '../utils/locationUtils';
import { LocationCoordinates, AddressDetails, LocationCache } from '../types';

interface LocationCacheState {
	cachedLocation: LocationCache | null;
	recentLocations: LocationCache[];
	isLoading: boolean;
	error: string | null;
}

export const useLocationCache = () => {
	const [cacheState, setCacheState] = useState<LocationCacheState>({
		cachedLocation: null,
		recentLocations: [],
		isLoading: false,
		error: null
	});

	// Load cached data on mount
	useEffect(() => {
		loadCachedData();
	}, []);

	const loadCachedData = useCallback(async () => {
		setCacheState(prev => ({ ...prev, isLoading: true, error: null }));

		try {
			const [cachedLocation, recentLocations] = await Promise.all([
				locationUtils.getCachedLocation(),
				locationUtils.getRecentLocations()
			]);

			setCacheState(prev => ({
				...prev,
				cachedLocation,
				recentLocations,
				isLoading: false
			}));
		} catch (error) {
			console.error('Load cached data error:', error);
			setCacheState(prev => ({
				...prev,
				error: 'Failed to load cached locations',
				isLoading: false
			}));
		}
	}, []);

	const cacheLocation = useCallback(async (
		coordinates: LocationCoordinates,
		address: AddressDetails
	) => {
		try {
			await locationUtils.cacheLocation(coordinates, address);

			// Update local state
			const newCache: LocationCache = {
				coordinates,
				address,
				timestamp: Date.now()
			};

			setCacheState(prev => ({
				...prev,
				cachedLocation: newCache
			}));
		} catch (error) {
			console.error('Cache location error:', error);
			setCacheState(prev => ({
				...prev,
				error: 'Failed to cache location'
			}));
		}
	}, []);

	const saveToRecentLocations = useCallback(async (
		coordinates: LocationCoordinates,
		address: AddressDetails
	) => {
		try {
			await locationUtils.saveToRecentLocations(coordinates, address);

			// Reload recent locations
			const recentLocations = await locationUtils.getRecentLocations();
			setCacheState(prev => ({
				...prev,
				recentLocations
			}));
		} catch (error) {
			console.error('Save to recent locations error:', error);
			setCacheState(prev => ({
				...prev,
				error: 'Failed to save to recent locations'
			}));
		}
	}, []);

	const clearCache = useCallback(async () => {
		try {
			await locationUtils.clearCache();
			setCacheState(prev => ({
				...prev,
				cachedLocation: null,
				recentLocations: []
			}));
		} catch (error) {
			console.error('Clear cache error:', error);
			setCacheState(prev => ({
				...prev,
				error: 'Failed to clear cache'
			}));
		}
	}, []);

	const findNearbyRecentLocation = useCallback((
		coordinates: LocationCoordinates,
		maxDistance: number = 100
	): LocationCache | null => {
		return cacheState.recentLocations.find(location =>
			locationUtils.isWithinDistance(
				location.coordinates,
				coordinates,
				maxDistance
			)
		) || null;
	}, [cacheState.recentLocations]);

	const getCachedLocationForCoordinates = useCallback((
		coordinates: LocationCoordinates
	): LocationCache | null => {
		// Check if cached location is close to the requested coordinates
		if (cacheState.cachedLocation) {
			const isNearby = locationUtils.isWithinDistance(
				cacheState.cachedLocation.coordinates,
				coordinates,
				50 // 50 meters
			);

			if (isNearby) {
				return cacheState.cachedLocation;
			}
		}

		// Check recent locations
		return findNearbyRecentLocation(coordinates, 50);
	}, [cacheState.cachedLocation, findNearbyRecentLocation]);

	return {
		...cacheState,
		loadCachedData,
		cacheLocation,
		saveToRecentLocations,
		clearCache,
		findNearbyRecentLocation,
		getCachedLocationForCoordinates,
		hasCachedLocation: !!cacheState.cachedLocation,
		hasRecentLocations: cacheState.recentLocations.length > 0
	};
};
