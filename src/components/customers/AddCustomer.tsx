import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import Map, { PROVIDER_GOOGLE } from 'react-native-maps';
import Toast from 'react-native-toast-message';
import { Alert, I18nManager, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useIsConnected } from 'react-native-offline';
import { Col, Col6, Row } from 'react-native-col';
import { useTranslation } from 'react-i18next';
import { CheckBox, PrimaryButton, Switch, TextField } from '../common';
import { VerifyOtpModal, UserExistsModal } from '../modals';
import { SelectLocationEnhanced } from '../mobile/select-location-enhanced';
import CustomersDetailItem from './CustomerDetailItem';
import { DropdownTablet } from '../common/DropdownTablet';
import { CheckMark, Offline, PlusVector } from '../../assets/svgs/icons';
import { customerAddNotifications, HORIZONTAL_DIMENS, USER_TYPES, VERTICAL_DIMENS } from '../../constants';
import { formatPhoneNumber, validatePhoneNumber } from '../../utils/libphonenumber';
import { getLanguages, getSalesPersonId, getTenantCountry, getUserType } from '../../redux/selectors';
import { addCustomer, customerNumberVerify, getExistsCustomers } from '../../redux/apis/customer';
import { colors, fonts } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { checkBadRequest } from '../../utils/helpers';

/* -------------------------------------------------------------------------- */
/*                 Used in online(tablet) and offline(tablet).                */
/* -------------------------------------------------------------------------- */
const AddCustomer = () => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const isConnected = useIsConnected();
	const userType = useAppSelector(getUserType);
	const tenantCountry = useAppSelector(getTenantCountry);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const preferredLanguages = useAppSelector(getLanguages);
	const { loading, priceList, salesPersons } = useAppSelector(state => state.customer);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const masterSettings = useAppSelector(state => state.setting.masterSettings);
	const [userExists, setUserExits] = useState<Array<any>>([]);
	const [showOTPModal, setShowOTPModal] = useState(false);
	const [locationModal, setLocationModal] = useState(false);
	const [verifiedMobile, setVerifiedMobile] = useState('');
	const [mobileValue, setMobileValue] = useState({ value: '', error: false, isValid: false, verified: false });
	const [shippingDetails, setShippingDetails] = useState<any>(null);
	const [customerType, setCustomerType] = useState<any>(priceList[0]);
	const [salesPerson, setSalesPerson] = useState<any>(salesPersons[0]);
	const [language, setLanguage] = useState<any>(preferredLanguages[0]);
	const [sendOtpLoader, setSendOtpLoader] = useState(false);
	const [details, setDetails] = useState({
		customerName: '',
		legalName: '',
		firstName: '',
		lastName: '',
		email: '',
		customerAppRequest: false,
		customerAppAccess: false
	});
	const [selection, setSelection] = useState({
		start: 0,
		end: 0
	});
	const [validForm, setValidForm] = useState(false);

	useEffect(() => {
		if (priceList.length === 0) {
			Alert.alert(t('Please Add Price List'));
		};
		if (userType !== USER_TYPES.SALES_APP && salesPersons.length === 0) {
			Alert.alert(t('please_add_sales_person'));
		};
	}, [priceList, salesPersons]);

	useEffect(() => {
		if (isEmpty(details.customerName) || isEmpty(details.legalName) || isEmpty(details.firstName) || isEmpty(details.lastName)) {
			setValidForm(false);
			return;
		}
		if (mobileValue.isValid === false || mobileValue.verified === false || shippingDetails === null) {
			setValidForm(false);
			return;
		}
		if (priceList.length === 0) {
			setValidForm(false);
			return;
		}
		setValidForm(true);
	}, [details, mobileValue, shippingDetails, priceList]);

	const isEmpty = (value: string) => {
		const trimmedValue = value.trim();
		if (trimmedValue === '' || trimmedValue === null || trimmedValue === undefined) {
			return true;
		}
		return false;
	};

	const onChangeDetails = (key: string, value: string) => {
		setDetails({ ...details, [key]: value });
	};

	const onToggleOtpModal = () => {
		setShowOTPModal(!showOTPModal);
	};

	const onToggleLocationModal = () => {
		setLocationModal(!locationModal);
	};

	const onChangeMobile = (text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);
		const isVerify = mobileNumber ? mobileNumber === verifiedMobile : false;
		setMobileValue({ ...mobileValue, value: mobileNumber, isValid: !!isValid, verified: isVerify });
		setSelection({ ...selection, start: mobileNumber.length, end: mobileNumber.length });
	};

	const handleSelection = () => {
		//console.log('mobile.value.length', mobileValue.value.length);
		setSelection({ ...selection, start: mobileValue.value.length, end: mobileValue.value.length });
	};

	const onChangeAppAccess = (isSwitch: boolean) => {
		if (isSwitch) setDetails({ ...details, customerAppAccess: !details.customerAppAccess });
		else setDetails({ ...details, customerAppRequest: !details.customerAppRequest });
	};

	const verifyMobileNumber = async () => {
		setSendOtpLoader(true);
		let number = mobileValue.value;
		const result = number.startsWith('0');
		result === true ? number = number.substring(1) : number;
		const requestBody = {
			action: 'SEND_OTP',
			mobileNumber: number,
			countryCode: tenantCountry.country_code
		};
		const response = await dispatch(customerNumberVerify(requestBody));
		if (!response.error) {
			onToggleOtpModal();
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
		setSendOtpLoader(false);
	};

	const onMobileVerifySuccess = () => {
		setMobileValue({ ...mobileValue, verified: true });
		setVerifiedMobile(mobileValue.value);
		onToggleOtpModal();
	};

	const onSelectLocation = (locationDetails: any) => {
		setShippingDetails(locationDetails);
		setLocationModal(false);
	};

	const checkCustomerExists = async () => {
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			countryCode: tenantCountry.country_code,
			mobileNumber: mobileValue.value
		};
		const response = await dispatch(getExistsCustomers(requestBody));
		if (!response.error) {
			if (response.payload.data && response.payload.data.length > 0) {
				setUserExits(response.payload.data);
				//setUserExits(customerExists);
			} else {
				onAddCustomer();
			}
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onConfirmAdd = () => {
		setUserExits([]);
		onAddCustomer();
	};

	const onAddCustomer = async () => {
		const salesId = userType !== USER_TYPES.SALES_APP ? salesPerson.value : salesPersonId;
		const requestBody = {
			...details,
			...shippingDetails,
			salesPersonId: salesId,
			shippingCountryId: tenantCountry._id,
			notifications: customerAddNotifications,
			countryCode: tenantCountry.country_code,
			deviceAccess: [],
			isActive: true,
			tenantId: currentRole?.tenant_id?._id,
			mobileNumber: mobileValue.value,
			preferredLanguage: language.value,
			priceListId: customerType.value,
			isVerified: true,
			appType: 'SALES_APP',
			customerCatalogMode: masterSettings?.catalog_mode ? true : false
		};
		if (requestBody.email === '') delete requestBody.email;
		const validRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
		if (requestBody.email && validRegex.test(requestBody.email.toLowerCase()) === false) {
			Alert.alert(t('invalid_email'));
			return;
		}
		const response = await dispatch(addCustomer(requestBody));
		if (!response.error) {
			Toast.show({
				type: 'success',
				text1: t(response.payload.message),
				position: 'top'
			});
			setDetails({
				customerName: '',
				legalName: '',
				firstName: '',
				lastName: '',
				email: '',
				customerAppRequest: details.customerAppRequest,
				customerAppAccess: details.customerAppAccess
			});
			setShippingDetails(null);
			setMobileValue({ value: '', error: false, isValid: false, verified: false });
			setLanguage(preferredLanguages[0].name);
			setCustomerType(priceList[0].name);
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	return (
		<View style={styles.container} >
			<Row.LR>
				<Text style={styles.header}>{t('customer_details')}</Text>
				<PrimaryButton
					style={styles.editInformation}
					disabled={(!validForm) || isConnected === false}
					onPress={checkCustomerExists}
					title={t('add_customer')}
					loading={loading}
					leftIcon={
						isConnected === false ? <Offline style={{ marginRight: HORIZONTAL_DIMENS._10 }} fill={colors.grey500} /> : undefined
					}
				/>
			</Row.LR>
			<TextField
				inputContainerStyle={styles.inputContainer}
				label={t('customer_name')}
				required
				value={details.customerName}
				onChangeText={(value) => onChangeDetails('customerName', value)}
				returnKeyType="done"
			/>
			<TextField
				inputContainerStyle={styles.inputContainer}
				label={t('legal_name')}
				required
				value={details.legalName}
				onChangeText={(value) => onChangeDetails('legalName', value)}
				returnKeyType="done"
			/>
			{userType !== USER_TYPES.SALES_APP &&
				<DropdownTablet
					label={t('sales_person')}
					required
					defaultIndex={0}
					options={salesPersons}
					defaultValue={salesPerson?.name}
					onChange={setSalesPerson}
					style={styles.customerTypeDropdown}
				/>
			}
			<DropdownTablet
				label={t('customer_type')}
				required
				defaultIndex={0}
				options={priceList}
				defaultValue={customerType?.name}
				onChange={setCustomerType}
				style={styles.customerTypeDropdown}
			/>
			<Row.L>
				<Text style={[styles.header, styles.mt24]}>{t('contact_person')}</Text>
			</Row.L>
			<TextField
				inputContainerStyle={styles.inputContainer}
				label={t('first_name')}
				required
				value={details.firstName}
				onChangeText={(value) => onChangeDetails('firstName', value)}
				returnKeyType="done"
			/>
			<TextField
				inputContainerStyle={styles.inputContainer}
				label={t('last_name')}
				required
				value={details.lastName}
				onChangeText={(value) => onChangeDetails('lastName', value)}
				returnKeyType="done"
			/>
			<TextField
				inputContainerStyle={styles.inputContainer}
				label={t('email_address')}
				value={details.email}
				onChangeText={(value) => onChangeDetails('email', value)}
			/>
			<DropdownTablet
				label={t('preferred_language')}
				required
				defaultIndex={0}
				defaultValue={language.name}
				onChange={setLanguage}
				options={preferredLanguages}
				style={styles.customerTypeDropdown}
			/>
			<View style={styles.mobileRow}>
				<Col.TB style={styles.mobileCountryCode}>
					<Text style={styles.mobileCountryCodeTxt}>{tenantCountry.country_code}</Text>
				</Col.TB>
				<TextField
					inputContainerStyle={styles.mobileNumber}
					label={t('mobile_number')}
					required
					value={mobileValue.value}
					selection={selection}
					onFocus={handleSelection}
					placeholder="5xxx"
					style={styles.mobileNumberInput}
					keyboardType="number-pad"
					returnKeyType="done"
					onChangeText={onChangeMobile}
				/>
				{
					!mobileValue.verified &&
					<PrimaryButton
						loading={sendOtpLoader}
						style={styles.verifyPhone}
						disabled={!mobileValue.isValid}
						onPress={verifyMobileNumber}
						title={t('verify_number')}
					/>
				}
				{
					mobileValue.verified &&
					<View
						style={styles.verifyNumber}
					>
						<CheckMark fill={colors.secondary} />
						<Text style={[styles.verifyPhoneTxt, !mobileValue.isValid && styles.disabled]}>{t('verified')}</Text>
					</View>
				}
			</View>
			<VerifyOtpModal
				isVisible={showOTPModal}
				countryCode={tenantCountry.country_code}
				mobileNumber={mobileValue.value}
				onCancel={onToggleOtpModal}
				onVerify={onMobileVerifySuccess}
			/>

			{
				mobileValue.verified && (masterSettings?.customer_app_access ? (
					<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(true)}>
						<Text style={styles.AppAccessText}>{t('app_access')}</Text>

						<Switch
							onValueChange={() => onChangeAppAccess(true)}
							value={details.customerAppAccess}
						/>
					</TouchableOpacity>
				) : (
					<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(false)}>
						<Text style={styles.AppAccessText}>{t('request_access')}</Text>

						<CheckBox checked={details.customerAppRequest} onChange={() => onChangeAppAccess(false)} />
					</TouchableOpacity>
				))
			}
			<Row.L>
				<Text style={[styles.header, styles.mt24]}>{t('location')}</Text>
			</Row.L>

			{
				!shippingDetails && <TouchableOpacity style={styles.addLocationBtn} onPress={onToggleLocationModal}>
					<View style={styles.plusCircle}>
						<PlusVector fill={colors.white} height={12} />
					</View>
					<Text style={styles.addLocationBtnText}>{t('add_location')}</Text>
				</TouchableOpacity>
			}
			{
				shippingDetails && <Row style={styles.mt24}>
					<Col6>
						<CustomersDetailItem label={t('address')} value={shippingDetails?.shippingAddress} />
						<Row.L style={styles.mt24}>
							<CustomersDetailItem label={t('region')} value={shippingDetails?.region} />
							<CustomersDetailItem label={t('city')} value={shippingDetails?.city} style={styles.ml40} />
						</Row.L>
						<CustomersDetailItem label={t('shipping_mobile_number')} value={shippingDetails ? formatPhoneNumber(shippingDetails.shippingCountryCode, shippingDetails.shippingMobileNumber) : ''} style={styles.mt24} />
						<View style={styles.mapContainer}>
							<Map
								provider={PROVIDER_GOOGLE}
								style={styles.map}
								region={{
									latitude: shippingDetails?.latitude,
									longitude: shippingDetails?.longitude,
									latitudeDelta: 0.015,
									longitudeDelta: 0.0121
								}}
								zoomEnabled={false}
								rotateEnabled={false}
								scrollEnabled={false}
								pitchEnabled={false}
							/>
						</View>
					</Col6>
				</Row>
			}

			<UserExistsModal
				users={userExists}
				onCancel={() => setUserExits([])}
				onConfirm={onConfirmAdd}
			/>

			<SelectLocationEnhanced
				isVisible={locationModal}
				onClose={onToggleLocationModal}
				onSelect={onSelectLocation}
				title={t('select_customer_location')}
				showShippingStep={true}
			/>

		</View >
	);
};

const styles = StyleSheet.create({
	title: {
		alignSelf: 'center',
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		marginBottom: VERTICAL_DIMENS._16,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	container: {
		backgroundColor: colors.white,
		borderRadius: 16,
		flexGrow: 1,
		paddingTop: 32,
		paddingBottom: 32,
		paddingHorizontal: 40,
		marginVertical: 42,
		marginHorizontal: HORIZONTAL_DIMENS._130,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.05,
		shadowRadius: 8,
		elevation: 1
	},
	header: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	editInformation: {
		backgroundColor: colors.secondary,
		paddingHorizontal: 30,
		paddingVertical: 13,
		flexDirection: 'row'
	},
	verifyPhone: {
		backgroundColor: colors.secondary,
		paddingHorizontal: 24,
		// paddingVertical: 9,
		alignSelf: 'center',
		position: 'absolute',
		right: 6,
		height: 36
	},
	verifyNumber: {
		alignItems: 'center',
		flexDirection: 'row',
		position: 'absolute',
		right: 20,
		height: 50
	},
	mt24: {
		marginTop: 24
	},
	ml40: {
		marginLeft: 40
	},
	requestAppAccess: {
		alignItems: 'center',
		backgroundColor: colors.grey100,
		borderRadius: 25,
		flexDirection: 'row',
		height: 50,
		justifyContent: 'space-between',
		marginTop: VERTICAL_DIMENS._22,
		paddingLeft: HORIZONTAL_DIMENS._20,
		paddingRight: HORIZONTAL_DIMENS._10
	},
	AppAccessText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._15,
		fontWeight: '500'
	},
	requestAccessContainer: {
		flex: 1,
		alignItems: 'flex-end',
		marginRight: 10
	},
	requestAccessText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '600'
	},
	requestAppAccessCheckbox: {
		height: 32,
		width: 32,
		backgroundColor: colors.white
	},
	mapContainer: {
		backgroundColor: colors.grayBG,
		borderRadius: 16,
		height: 260,
		marginTop: 20,
		//width: 300,
		overflow: 'hidden'
	},
	map: {
		height: '100%',
		width: '100%'
	},
	inputContainer: {
		marginTop: 25
	},
	customerTypeDropdown: {
		marginTop: 24
	},
	mobileRow: {
		marginTop: 25,
		flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row'
	},
	mobileCountryCode: {
		justifyContent: 'center',
		position: 'absolute',
		left: 20,
		height: 50
	},
	mobileCountryCodeTxt: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16
		//writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	mobileNumber: {
		width: '100%'
	},
	mobileNumberInput: {
		// paddingLeft: 70,
		// writingDirection: 'auto',
		writingDirection: 'ltr',
		textAlign: 'left',
		paddingRight: I18nManager.isRTL ? 70 : 0,
		paddingLeft: I18nManager.isRTL ? 0 : 70
	},
	verifyPhoneTxt: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._14,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	disabled: {
		color: colors.grey400
	},
	customerAccessText: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	plusCircle: {
		alignItems: 'center',
		backgroundColor: colors.secondary,
		borderRadius: 12,
		justifyContent: 'center',
		height: 24,
		width: 24
	},
	addLocationBtn: {
		alignItems: 'center',
		backgroundColor: colors.grey100,
		borderRadius: 12,
		flexDirection: 'row',
		justifyContent: 'center',
		height: 50,
		marginTop: 20
	},
	addLocationBtnText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._16,
		marginLeft: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	flexRow: {
		flexDirection: 'row',
		alignItems: 'center'
	}
});

export default AddCustomer;
