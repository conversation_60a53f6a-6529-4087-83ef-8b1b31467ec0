import { useCallback, useEffect, useRef, useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getCurrentLocation, Geocoder } from '../../../utils/functions';
import {
	LocationCoordinates,
	LocationRegion,
	AddressDetails,
	LocationState,
	LocationError
} from '../types';

const DEFAULT_REGION: LocationRegion = {
	latitude: 37.78825,
	longitude: -122.4324,
	latitudeDelta: 0.015,
	longitudeDelta: 0.0121
};

const GEOCODING_DEBOUNCE_MS = 500;

export const useLocationManager = (initialLocation?: LocationCoordinates) => {
	const { t } = useTranslation();
	const geocodingTimeoutRef = useRef<NodeJS.Timeout>();

	const [locationState, setLocationState] = useState<LocationState>({
		region: initialLocation
			? { ...DEFAULT_REGION, ...initialLocation }
			: DEFAULT_REGION,
		addressDetails: {
			city: '',
			shippingCityId: '',
			region: '',
			shippingRegionId: '',
			country: '',
			shippingAddress: '',
			regionCode: ''
		},
		isLoading: false,
		error: null,
		hasLocationPermission: false
	});

	const reverseGeocode = useCallback(async (coordinates: LocationCoordinates) => {
		if (geocodingTimeoutRef.current) {
			clearTimeout(geocodingTimeoutRef.current);
		}

		geocodingTimeoutRef.current = setTimeout(async () => {
			setLocationState(prev => ({ ...prev, isLoading: true, error: null }));

			try {
				const response = await Geocoder.from(coordinates.latitude, coordinates.longitude);
				const result = response.results[0];
				const addressDetails = Geocoder.getAddressComponents(result);

				setLocationState(prev => ({
					...prev,
					addressDetails,
					isLoading: false
				}));
			} catch (error) {
				console.warn('Geocoding error:', error);
				setLocationState(prev => ({
					...prev,
					error: 'Failed to get address details',
					isLoading: false
				}));
			}
		}, GEOCODING_DEBOUNCE_MS);
	}, []);

	const updateRegion = useCallback((newRegion: LocationRegion) => {
		setLocationState(prev => ({ ...prev, region: newRegion }));
		reverseGeocode({
			latitude: newRegion.latitude,
			longitude: newRegion.longitude
		});
	}, [reverseGeocode]);

	const getCurrentUserLocation = useCallback(async (): Promise<LocationCoordinates | null> => {
		try {
			setLocationState(prev => ({ ...prev, isLoading: true, error: null }));

			const location: any = await getCurrentLocation();

			if (location?.coords) {
				const coordinates: LocationCoordinates = {
					latitude: location.coords.latitude,
					longitude: location.coords.longitude
				};

				const newRegion: LocationRegion = {
					...coordinates,
					latitudeDelta: 0.015,
					longitudeDelta: 0.0121
				};

				setLocationState(prev => ({
					...prev,
					region: newRegion,
					hasLocationPermission: true,
					isLoading: false
				}));

				reverseGeocode(coordinates);
				return coordinates;
			} else {
				throw new Error('Location not available');
			}
		} catch (error) {
			console.warn('Get current location error:', error);
			setLocationState(prev => ({
				...prev,
				error: 'Failed to get current location',
				hasLocationPermission: false,
				isLoading: false
			}));

			Alert.alert(
				t('location_error'),
				t('failed_to_get_location'),
				[{ text: t('ok') }]
			);

			return null;
		}
	}, [t, reverseGeocode]);

	const animateToLocation = useCallback((coordinates: LocationCoordinates) => {
		const newRegion: LocationRegion = {
			...coordinates,
			latitudeDelta: 0.015,
			longitudeDelta: 0.0121
		};

		updateRegion(newRegion);
		return newRegion;
	}, [updateRegion]);

	const setError = useCallback((error: string | null) => {
		setLocationState(prev => ({ ...prev, error }));
	}, []);

	const clearError = useCallback(() => {
		setLocationState(prev => ({ ...prev, error: null }));
	}, []);

	// Initialize with current location if no initial location provided
	useEffect(() => {
		if (!initialLocation) {
			getCurrentUserLocation();
		}
	}, [initialLocation, getCurrentUserLocation]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (geocodingTimeoutRef.current) {
				clearTimeout(geocodingTimeoutRef.current);
			}
		};
	}, []);

	return {
		locationState,
		updateRegion,
		getCurrentUserLocation,
		animateToLocation,
		setError,
		clearError,
		isLoading: locationState.isLoading,
		hasError: !!locationState.error,
		errorMessage: locationState.error,
		region: locationState.region,
		addressDetails: locationState.addressDetails,
		hasLocationPermission: locationState.hasLocationPermission
	};
};
