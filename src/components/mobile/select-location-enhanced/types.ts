export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface LocationRegion extends LocationCoordinates {
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface AddressDetails {
  city: string;
  shippingCityId: string;
  region: string;
  shippingRegionId: string;
  country: string;
  shippingAddress: string;
  regionCode: string;
}

export interface PlaceResult {
  place_id: string;
  description: string;
  structured_formatting?: {
    main_text: string;
    secondary_text: string;
  };
}

export interface LocationSearchResult {
  lat: number;
  lng: number;
  address: string;
  placeId?: string;
}

export interface SelectLocationProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (data: LocationSelectionData) => void;
  customerRoleId?: string;
  shippingNumber?: string;
  initialLocation?: LocationCoordinates;
  title?: string;
  showShippingStep?: boolean;
}

export interface LocationSelectionData extends AddressDetails, LocationCoordinates {
  shippingMobileNumber?: string;
  shippingCountryCode?: string;
}

export interface LocationStep {
  id: string;
  title: string;
  component: React.ComponentType<any>;
}

export interface LocationCache {
  coordinates: LocationCoordinates;
  address: AddressDetails;
  timestamp: number;
}

export interface SearchState {
  query: string;
  results: PlaceResult[];
  isLoading: boolean;
  error: string | null;
}

export interface LocationState {
  region: LocationRegion;
  addressDetails: AddressDetails;
  isLoading: boolean;
  error: string | null;
  hasLocationPermission: boolean;
}

export interface MapState {
  isMapReady: boolean;
  isUserInteracting: boolean;
  animationInProgress: boolean;
}

export enum LocationStep {
  MAP_SELECTION = 'map_selection',
  LOCATION_CONFIRMATION = 'location_confirmation',
  SHIPPING_DETAILS = 'shipping_details'
}

export interface LocationError {
  code: string;
  message: string;
  type: 'PERMISSION' | 'NETWORK' | 'GEOCODING' | 'API' | 'UNKNOWN';
}
