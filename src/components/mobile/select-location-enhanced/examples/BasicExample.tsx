import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { SelectLocationEnhanced } from '../SelectLocationEnhanced';
import { LocationSelectionData } from '../types';
import { colors, fonts } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';

/**
 * Basic example of using the SelectLocationEnhanced component
 */
const BasicExample: React.FC = () => {
	const [isVisible, setIsVisible] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<LocationSelectionData | null>(null);

	const handleLocationSelect = (locationData: LocationSelectionData) => {
		console.log('Selected location:', locationData);
		setSelectedLocation(locationData);
		setIsVisible(false);
	};

	const handleOpenLocationSelector = () => {
		setIsVisible(true);
	};

	return (
		<View style={styles.container}>
			<TouchableOpacity
				style={styles.button}
				onPress={handleOpenLocationSelector}
			>
				<Text style={styles.buttonText}>Select Location</Text>
			</TouchableOpacity>

			{selectedLocation && (
				<View style={styles.selectedLocationContainer}>
					<Text style={styles.selectedLocationTitle}>Selected Location:</Text>
					<Text style={styles.selectedLocationText}>
						{selectedLocation.shippingAddress}
					</Text>
					<Text style={styles.selectedLocationDetails}>
						{selectedLocation.city}, {selectedLocation.region}
					</Text>
					<Text style={styles.selectedLocationCoords}>
						Lat: {selectedLocation.latitude.toFixed(6)},
						Lng: {selectedLocation.longitude.toFixed(6)}
					</Text>
				</View>
			)}

			<SelectLocationEnhanced
				isVisible={isVisible}
				onClose={() => setIsVisible(false)}
				onSelect={handleLocationSelect}
				title="Choose Your Location"
				showShippingStep={false} // Skip shipping step for basic example
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: HORIZONTAL_DIMENS._20,
		justifyContent: 'center'
	},
	button: {
		backgroundColor: colors.primary,
		paddingHorizontal: HORIZONTAL_DIMENS._24,
		paddingVertical: VERTICAL_DIMENS._16,
		borderRadius: 8,
		alignItems: 'center',
		marginBottom: VERTICAL_DIMENS._20
	},
	buttonText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '500'
	},
	selectedLocationContainer: {
		backgroundColor: colors.grey100,
		padding: HORIZONTAL_DIMENS._16,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: colors.grey200
	},
	selectedLocationTitle: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '600',
		marginBottom: VERTICAL_DIMENS._8
	},
	selectedLocationText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		marginBottom: VERTICAL_DIMENS._4
	},
	selectedLocationDetails: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._4
	},
	selectedLocationCoords: {
		color: colors.grey500,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._10
	}
});

export default BasicExample;
