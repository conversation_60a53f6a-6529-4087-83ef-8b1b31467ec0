import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import React, { useEffect, useState, memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
	I18nManager,
	StyleSheet,
	Text,
	View,
	TouchableOpacity
} from 'react-native';
import { PrimaryButton } from '../../../common';
import {
	MobileIcon,
	ArrowLeft,
	CheckGreen,
	Info
} from '../../../../assets/svgs/icons';
import { useAppSelector } from '../../../../redux/hooks';
import { getTenantCountry } from '../../../../redux/selectors';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';
import { colors, fonts } from '../../../../utils/theme';
import { validatePhoneNumber } from '../../../../utils/libphonenumber';

interface ShippingNumberEnhancedProps {
	onSubmit: (mobile: string, countryCode: string) => void;
	loading?: boolean;
	shippingNumber?: string;
	onBack?: () => void;
}

const ShippingNumberEnhanced: React.FC<ShippingNumberEnhancedProps> = memo(({
	onSubmit,
	loading = false,
	shippingNumber,
	onBack
}) => {
	const { t } = useTranslation();
	const tenantCountry = useAppSelector(getTenantCountry);

	const [mobile, setMobile] = useState({
		value: '',
		error: false,
		isValid: false,
		touched: false
	});

	// Initialize with existing shipping number
	useEffect(() => {
		if (shippingNumber) {
			const mobileNumber = shippingNumber.toString().replace(/[^0-9]/g, '');
			const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);
			setMobile({
				value: mobileNumber,
				error: false,
				isValid: !!isValid,
				touched: false
			});
		}
	}, [shippingNumber, tenantCountry.country_code]);

	const handleNumberChange = useCallback((text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);

		setMobile(prev => ({
			...prev,
			value: mobileNumber,
			error: prev.touched && !isValid,
			isValid: !!isValid
		}));
	}, [tenantCountry.country_code]);

	const handleBlur = useCallback(() => {
		setMobile(prev => ({
			...prev,
			touched: true,
			error: !prev.isValid && prev.value.length > 0
		}));
	}, []);

	const handleConfirm = useCallback(() => {
		if (mobile.isValid) {
			onSubmit(mobile.value, tenantCountry.country_code);
		}
	}, [mobile.isValid, mobile.value, tenantCountry.country_code, onSubmit]);

	const getValidationIcon = () => {
		if (!mobile.touched || mobile.value.length === 0) return null;

		return mobile.isValid ? (
			<CheckGreen fill={colors.success} width={20} height={20} />
		) : (
			<Info fill={colors.danger} width={20} height={20} />
		);
	};

	const getValidationMessage = () => {
		if (!mobile.touched || mobile.value.length === 0) return null;

		if (mobile.error) {
			return (
				<Text style={styles.errorMessage}>
					{t('invalid_phone_number')}
				</Text>
			);
		}

		if (mobile.isValid) {
			return (
				<Text style={styles.successMessage}>
					{t('valid_phone_number')}
				</Text>
			);
		}

		return null;
	};

	return (
		<View style={styles.container}>
			{/* Header with back button and title */}
			<View style={styles.header}>
				{onBack && (
					<TouchableOpacity
						onPress={onBack}
						style={styles.backButton}
						disabled={loading}
						hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
					>
						<ArrowLeft
							fill={colors.primary}
							width={24}
							height={24}
							style={{
								transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
								opacity: loading ? 0.5 : 1
							}}
						/>
					</TouchableOpacity>
				)}

				<View style={styles.headerContent}>
					<Text style={styles.title}>{t('shipping_mobile_number')}</Text>
				</View>

				{/* Placeholder for balance */}
				<View style={styles.headerPlaceholder} />
			</View>

			{/* Content Section */}
			<View style={styles.content}>
				{/* Icon and Description */}
				<View style={styles.descriptionSection}>
					<View style={styles.iconContainer}>
						<MobileIcon fill={colors.primary} width={32} height={32} />
					</View>

					<Text style={styles.subtitle}>
						{t('enter_mobile_number_for_delivery')}
					</Text>
				</View>

				{/* Phone Input Container */}
				<View style={styles.inputSection}>
					<Text style={styles.inputLabel}>{t('mobile_number')}</Text>

					<View style={[
						styles.inputContainer,
						mobile.error && styles.inputContainerError,
						mobile.isValid && mobile.touched && styles.inputContainerSuccess
					]}>
						<View style={styles.countryCodeContainer}>
							<Text style={styles.countryCodeText}>
								{tenantCountry.country_code}
							</Text>
						</View>

						<BottomSheetTextInput
							style={styles.mobileInput}
							keyboardType="number-pad"
							returnKeyType="done"
							value={mobile.value}
							onChangeText={handleNumberChange}
							onBlur={handleBlur}
							placeholder={t('enter_mobile_number')}
							placeholderTextColor={colors.romanSilver}
							editable={!loading}
							autoFocus={true}
						/>

						<View style={styles.validationIcon}>
							{getValidationIcon()}
						</View>
					</View>

					{/* Validation Message */}
					{getValidationMessage()}
				</View>

				{/* Helper Text */}
				<View style={styles.helperSection}>
					<Text style={styles.helperText}>
						{t('mobile_number_delivery_info')}
					</Text>
				</View>
			</View>

			{/* Bottom Action */}
			<View style={styles.bottomAction}>
				<PrimaryButton
					title={t('confirm')}
					onPress={handleConfirm}
					style={styles.confirmButton}
					titleStyle={styles.confirmButtonText}
					disabled={!mobile.isValid || loading}
					loading={loading}
				/>
			</View>
		</View>
	);
});

ShippingNumberEnhanced.displayName = 'ShippingNumberEnhanced';

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: colors.white
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._12,
		borderBottomWidth: 1,
		borderBottomColor: colors.brightGray2
	},
	backButton: {
		padding: HORIZONTAL_DIMENS._8,
		borderRadius: 8,
		backgroundColor: colors.lightBackground
	},
	headerContent: {
		flex: 1,
		alignItems: 'center'
	},
	headerPlaceholder: {
		width: 40 // Same width as back button for balance
	},
	title: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: 18,
		textAlign: 'center'
	},
	content: {
		flex: 1,
		paddingHorizontal: HORIZONTAL_DIMENS._24,
		paddingTop: VERTICAL_DIMENS._24
	},
	descriptionSection: {
		alignItems: 'center',
		marginBottom: VERTICAL_DIMENS._32
	},
	iconContainer: {
		width: 64,
		height: 64,
		borderRadius: 32,
		backgroundColor: colors.lightBackground,
		alignItems: 'center',
		justifyContent: 'center',
		marginBottom: VERTICAL_DIMENS._16
	},
	subtitle: {
		color: colors.romanSilver,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: 16,
		textAlign: 'center',
		lineHeight: 22,
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	inputSection: {
		marginBottom: VERTICAL_DIMENS._24
	},
	inputLabel: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: 14,
		marginBottom: VERTICAL_DIMENS._8,
		fontWeight: '500'
	},
	inputContainer: {
		flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
		alignItems: 'center',
		backgroundColor: colors.lightBackground,
		borderRadius: 12,
		borderWidth: 1,
		borderColor: colors.brightGray2,
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._4,
		marginBottom: VERTICAL_DIMENS._8
	},
	inputContainerError: {
		borderColor: colors.danger,
		backgroundColor: colors.dangerLight
	},
	inputContainerSuccess: {
		borderColor: colors.success,
		backgroundColor: colors.successLight
	},
	countryCodeContainer: {
		paddingRight: HORIZONTAL_DIMENS._12,
		borderRightWidth: 1,
		borderRightColor: colors.brightGray2,
		marginRight: HORIZONTAL_DIMENS._12
	},
	countryCodeText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: 16
	},
	mobileInput: {
		flex: 1,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: 16,
		height: 48,
		paddingVertical: VERTICAL_DIMENS._12
	},
	validationIcon: {
		width: 24,
		alignItems: 'center',
		justifyContent: 'center'
	},
	errorMessage: {
		color: colors.danger,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	successMessage: {
		color: colors.success,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	helperSection: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		paddingVertical: VERTICAL_DIMENS._16
	},
	helperText: {
		color: colors.romanSilver,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: 14,
		textAlign: 'center',
		lineHeight: 20,
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	bottomAction: {
		paddingHorizontal: HORIZONTAL_DIMENS._24,
		paddingVertical: VERTICAL_DIMENS._16,
		borderTopWidth: 1,
		borderTopColor: colors.brightGray2,
		backgroundColor: colors.white
	},
	confirmButton: {
		borderRadius: 12,
		paddingVertical: VERTICAL_DIMENS._16
	},
	confirmButtonText: {
		fontSize: 16,
		fontWeight: '600',
		textTransform: 'uppercase'
	}
});

export { ShippingNumberEnhanced };
