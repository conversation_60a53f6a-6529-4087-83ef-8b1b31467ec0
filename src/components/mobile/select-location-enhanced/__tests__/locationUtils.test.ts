import { locationUtils } from '../utils/locationUtils';
import { LocationCoordinates, AddressDetails } from '../types';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
	setItem: jest.fn(),
	getItem: jest.fn(),
	multiRemove: jest.fn()
}));

describe('locationUtils', () => {
	const mockCoordinates1: LocationCoordinates = {
		latitude: 37.78825,
		longitude: -122.4324
	};

	const mockCoordinates2: LocationCoordinates = {
		latitude: 37.78925,
		longitude: -122.4334
	};

	const mockAddress: AddressDetails = {
		city: 'San Francisco',
		shippingCityId: 'sf_123',
		region: 'California',
		shippingRegionId: 'ca_123',
		country: 'United States',
		shippingAddress: '123 Market St, San Francisco, CA',
		regionCode: 'CA'
	};

	describe('calculateDistance', () => {
		it('should calculate distance between two coordinates', () => {
			const distance = locationUtils.calculateDistance(mockCoordinates1, mockCoordinates2);
			expect(distance).toBeGreaterThan(0);
			expect(distance).toBeLessThan(200); // Should be less than 200 meters
		});

		it('should return 0 for same coordinates', () => {
			const distance = locationUtils.calculateDistance(mockCoordinates1, mockCoordinates1);
			expect(distance).toBe(0);
		});
	});

	describe('isWithinDistance', () => {
		it('should return true for coordinates within distance', () => {
			const isWithin = locationUtils.isWithinDistance(
				mockCoordinates1,
				mockCoordinates2,
				200
			);
			expect(isWithin).toBe(true);
		});

		it('should return false for coordinates outside distance', () => {
			const isWithin = locationUtils.isWithinDistance(
				mockCoordinates1,
				mockCoordinates2,
				50
			);
			expect(isWithin).toBe(false);
		});
	});

	describe('formatCoordinates', () => {
		it('should format coordinates correctly', () => {
			const formatted = locationUtils.formatCoordinates(mockCoordinates1);
			expect(formatted).toBe('37.788250, -122.432400');
		});
	});

	describe('isValidCoordinates', () => {
		it('should validate correct coordinates', () => {
			expect(locationUtils.isValidCoordinates(mockCoordinates1)).toBe(true);
		});

		it('should reject invalid latitude', () => {
			expect(locationUtils.isValidCoordinates({
				latitude: 91,
				longitude: -122.4324
			})).toBe(false);
		});

		it('should reject invalid longitude', () => {
			expect(locationUtils.isValidCoordinates({
				latitude: 37.78825,
				longitude: 181
			})).toBe(false);
		});
	});

	describe('isCompleteAddress', () => {
		it('should return true for complete address', () => {
			expect(locationUtils.isCompleteAddress(mockAddress)).toBe(true);
		});

		it('should return false for incomplete address', () => {
			const incompleteAddress = { ...mockAddress, city: '' };
			expect(locationUtils.isCompleteAddress(incompleteAddress)).toBe(false);
		});
	});

	describe('getAddressSummary', () => {
		it('should create address summary', () => {
			const summary = locationUtils.getAddressSummary(mockAddress);
			expect(summary).toBe('San Francisco, California, United States');
		});

		it('should fallback to shipping address if parts missing', () => {
			const incompleteAddress = {
				...mockAddress,
				city: '',
				region: '',
				country: ''
			};
			const summary = locationUtils.getAddressSummary(incompleteAddress);
			expect(summary).toBe('123 Market St, San Francisco, CA');
		});

		it('should return unknown location for empty address', () => {
			const emptyAddress = {
				city: '',
				shippingCityId: '',
				region: '',
				shippingRegionId: '',
				country: '',
				shippingAddress: '',
				regionCode: ''
			};
			const summary = locationUtils.getAddressSummary(emptyAddress);
			expect(summary).toBe('Unknown location');
		});
	});

	describe('generateLocationKey', () => {
		it('should generate consistent location key', () => {
			const key1 = locationUtils.generateLocationKey(mockCoordinates1);
			const key2 = locationUtils.generateLocationKey(mockCoordinates1);
			expect(key1).toBe(key2);
			expect(key1).toBe('37.7883_-122.4324');
		});
	});
});
