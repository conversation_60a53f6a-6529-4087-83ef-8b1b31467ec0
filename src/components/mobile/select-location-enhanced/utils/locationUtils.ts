import AsyncStorage from '@react-native-async-storage/async-storage';
import { LocationCoordinates, LocationCache, AddressDetails } from '../types';

const LOCATION_CACHE_KEY = '@location_cache';
const RECENT_LOCATIONS_KEY = '@recent_locations';
const MAX_RECENT_LOCATIONS = 5;
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

export const locationUtils = {
	/**
   * Calculate distance between two coordinates in meters
   */
	calculateDistance: (coord1: LocationCoordinates, coord2: LocationCoordinates): number => {
		const R = 6371e3; // Earth's radius in meters
		const φ1 = coord1.latitude * Math.PI / 180;
		const φ2 = coord2.latitude * Math.PI / 180;
		const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;
		const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;

		const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
			Math.cos(φ1) * Math.cos(φ2) *
			Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

		return R * c;
	},

	/**
   * Check if two coordinates are within a certain distance
   */
	isWithinDistance: (
		coord1: LocationCoordinates,
		coord2: LocationCoordinates,
		maxDistance: number = 100
	): boolean => {
		return locationUtils.calculateDistance(coord1, coord2) <= maxDistance;
	},

	/**
   * Format coordinates for display
   */
	formatCoordinates: (coordinates: LocationCoordinates): string => {
		return `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
	},

	/**
   * Validate coordinates
   */
	isValidCoordinates: (coordinates: LocationCoordinates): boolean => {
		return (
			coordinates.latitude >= -90 &&
			coordinates.latitude <= 90 &&
			coordinates.longitude >= -180 &&
			coordinates.longitude <= 180
		);
	},

	/**
   * Cache location data
   */
	cacheLocation: async (coordinates: LocationCoordinates, address: AddressDetails): Promise<void> => {
		try {
			const cache: LocationCache = {
				coordinates,
				address,
				timestamp: Date.now()
			};
			await AsyncStorage.setItem(LOCATION_CACHE_KEY, JSON.stringify(cache));
		} catch (error) {
			console.warn('Failed to cache location:', error);
		}
	},

	/**
   * Get cached location if still valid
   */
	getCachedLocation: async (): Promise<LocationCache | null> => {
		try {
			const cached = await AsyncStorage.getItem(LOCATION_CACHE_KEY);
			if (cached) {
				const cache: LocationCache = JSON.parse(cached);
				const isExpired = Date.now() - cache.timestamp > CACHE_EXPIRY_MS;

				if (!isExpired && locationUtils.isValidCoordinates(cache.coordinates)) {
					return cache;
				}
			}
		} catch (error) {
			console.warn('Failed to get cached location:', error);
		}
		return null;
	},

	/**
   * Save location to recent locations
   */
	saveToRecentLocations: async (coordinates: LocationCoordinates, address: AddressDetails): Promise<void> => {
		try {
			const recent = await locationUtils.getRecentLocations();
			const newLocation: LocationCache = {
				coordinates,
				address,
				timestamp: Date.now()
			};

			// Remove duplicate if exists
			const filtered = recent.filter(loc =>
				!locationUtils.isWithinDistance(loc.coordinates, coordinates, 50)
			);

			// Add new location at the beginning
			const updated = [newLocation, ...filtered].slice(0, MAX_RECENT_LOCATIONS);

			await AsyncStorage.setItem(RECENT_LOCATIONS_KEY, JSON.stringify(updated));
		} catch (error) {
			console.warn('Failed to save recent location:', error);
		}
	},

	/**
   * Get recent locations
   */
	getRecentLocations: async (): Promise<LocationCache[]> => {
		try {
			const recent = await AsyncStorage.getItem(RECENT_LOCATIONS_KEY);
			if (recent) {
				const locations: LocationCache[] = JSON.parse(recent);
				return locations.filter(loc => locationUtils.isValidCoordinates(loc.coordinates));
			}
		} catch (error) {
			console.warn('Failed to get recent locations:', error);
		}
		return [];
	},

	/**
   * Clear location cache
   */
	clearCache: async (): Promise<void> => {
		try {
			await AsyncStorage.multiRemove([LOCATION_CACHE_KEY, RECENT_LOCATIONS_KEY]);
		} catch (error) {
			console.warn('Failed to clear location cache:', error);
		}
	},

	/**
   * Generate a unique key for a location
   */
	generateLocationKey: (coordinates: LocationCoordinates): string => {
		return `${coordinates.latitude.toFixed(4)}_${coordinates.longitude.toFixed(4)}`;
	},

	/**
   * Check if address is complete
   */
	isCompleteAddress: (address: AddressDetails): boolean => {
		return !!(address.city && address.region && address.shippingAddress);
	},

	/**
   * Get address summary for display
   */
	getAddressSummary: (address: AddressDetails): string => {
		const parts = [address.city, address.region, address.country].filter(Boolean);
		return parts.join(', ') || address.shippingAddress || 'Unknown location';
	}
};
