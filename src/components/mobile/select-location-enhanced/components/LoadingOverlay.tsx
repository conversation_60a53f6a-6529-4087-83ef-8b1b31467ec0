import React, { memo } from 'react';
import {
	StyleSheet,
	View,
	Text,
	ActivityIndicator
} from 'react-native';
import { colors, fonts } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';

interface LoadingOverlayProps {
	message?: string;
	visible?: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = memo(({
	message = 'Loading...',
	visible = true
}) => {
	if (!visible) return null;

	return (
		<View style={styles.overlay}>
			<View style={styles.container}>
				<ActivityIndicator
					size="large"
					color={colors.primary}
					style={styles.spinner}
				/>
				<Text style={styles.message}>{message}</Text>
			</View>
		</View>
	);
});

LoadingOverlay.displayName = 'LoadingOverlay';

const styles = StyleSheet.create({
	overlay: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: colors.backdropBlur,
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 9999
	},
	container: {
		backgroundColor: colors.white,
		borderRadius: 12,
		padding: HORIZONTAL_DIMENS._24,
		alignItems: 'center',
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 4
		},
		shadowOpacity: 0.2,
		shadowRadius: 8,
		elevation: 8,
		minWidth: 120
	},
	spinner: {
		marginBottom: VERTICAL_DIMENS._12
	},
	message: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center'
	}
});

export { LoadingOverlay };
