import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import React, { useEffect, useState, memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
	I18nManager,
	StyleSheet,
	Text,
	View,
	TouchableOpacity
} from 'react-native';
import { PrimaryButton } from '../../../common';
import {
	MobileIcon,
	ArrowLeft,
	CheckCircle,
	AlertCircle
} from '../../../../assets/svgs/icons';
import { useAppSelector } from '../../../../redux/hooks';
import { getTenantCountry } from '../../../../redux/selectors';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';
import { colors, fonts } from '../../../../utils/theme';
import { validatePhoneNumber } from '../../../../utils/libphonenumber';

interface ShippingNumberEnhancedProps {
	onSubmit: (mobile: string, countryCode: string) => void;
	loading?: boolean;
	shippingNumber?: string;
	onBack?: () => void;
}

const ShippingNumberEnhanced: React.FC<ShippingNumberEnhancedProps> = memo(({
	onSubmit,
	loading = false,
	shippingNumber,
	onBack
}) => {
	const { t } = useTranslation();
	const tenantCountry = useAppSelector(getTenantCountry);

	const [mobile, setMobile] = useState({
		value: '',
		error: false,
		isValid: false,
		touched: false
	});

	// Initialize with existing shipping number
	useEffect(() => {
		if (shippingNumber) {
			const mobileNumber = shippingNumber.toString().replace(/[^0-9]/g, '');
			const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);
			setMobile({
				value: mobileNumber,
				error: false,
				isValid: !!isValid,
				touched: false
			});
		}
	}, [shippingNumber, tenantCountry.country_code]);

	const handleNumberChange = useCallback((text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);

		setMobile(prev => ({
			...prev,
			value: mobileNumber,
			error: prev.touched && !isValid,
			isValid: !!isValid
		}));
	}, [tenantCountry.country_code]);

	const handleBlur = useCallback(() => {
		setMobile(prev => ({
			...prev,
			touched: true,
			error: !prev.isValid && prev.value.length > 0
		}));
	}, []);

	const handleConfirm = useCallback(() => {
		if (mobile.isValid) {
			onSubmit(mobile.value, tenantCountry.country_code);
		}
	}, [mobile.isValid, mobile.value, tenantCountry.country_code, onSubmit]);

	const getValidationIcon = () => {
		if (!mobile.touched || mobile.value.length === 0) return null;

		return mobile.isValid ? (
			<CheckCircle fill={colors.success} width={20} height={20} />
		) : (
			<AlertCircle fill={colors.danger} width={20} height={20} />
		);
	};

	const getValidationMessage = () => {
		if (!mobile.touched || mobile.value.length === 0) return null;

		if (mobile.error) {
			return (
				<Text style={styles.errorMessage}>
					{t('invalid_phone_number')}
				</Text>
			);
		}

		if (mobile.isValid) {
			return (
				<Text style={styles.successMessage}>
					{t('valid_phone_number')}
				</Text>
			);
		}

		return null;
	};

	return (
		<View style={styles.container}>
			{/* Header with back button */}
			{onBack && (
				<View style={styles.header}>
					<TouchableOpacity
						onPress={onBack}
						style={styles.backButton}
						disabled={loading}
					>
						<ArrowLeft
							fill={colors.primary}
							style={{
								transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
								opacity: loading ? 0.5 : 1
							}}
						/>
					</TouchableOpacity>
				</View>
			)}

			<Text style={styles.title}>{t('shipping_mobile_number')}</Text>

			<Text style={styles.subtitle}>
				{t('enter_mobile_number_for_delivery')}
			</Text>

			{/* Phone Input Container */}
			<View style={[
				styles.inputContainer,
				mobile.error && styles.inputContainerError,
				mobile.isValid && mobile.touched && styles.inputContainerSuccess
			]}>
				<MobileIcon fill={colors.primary} width={20} height={20} />

				<Text style={styles.countryCodeText}>
					{tenantCountry.country_code}
				</Text>

				<BottomSheetTextInput
					style={styles.mobileInput}
					keyboardType="number-pad"
					returnKeyType="done"
					value={mobile.value}
					onChangeText={handleNumberChange}
					onBlur={handleBlur}
					placeholder={t('enter_mobile_number')}
					placeholderTextColor={colors.grey400}
					editable={!loading}
				/>

				<View style={styles.validationIcon}>
					{getValidationIcon()}
				</View>
			</View>

			{/* Validation Message */}
			{getValidationMessage()}

			{/* Helper Text */}
			<Text style={styles.helperText}>
				{t('mobile_number_delivery_info')}
			</Text>

			{/* Confirm Button */}
			<PrimaryButton
				title={t('confirm')}
				onPress={handleConfirm}
				style={styles.confirmButton}
				titleStyle={styles.confirmButtonText}
				disabled={!mobile.isValid || loading}
				loading={loading}
				loadingColor={colors.grey600}
			/>
		</View>
	);
});

ShippingNumberEnhanced.displayName = 'ShippingNumberEnhanced';

const styles = StyleSheet.create({
	container: {
		paddingVertical: VERTICAL_DIMENS._16
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		marginBottom: VERTICAL_DIMENS._16
	},
	backButton: {
		padding: 8,
		marginRight: HORIZONTAL_DIMENS._8
	},
	title: {
		alignSelf: 'center',
		color: colors.primary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	subtitle: {
		alignSelf: 'center',
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		marginBottom: VERTICAL_DIMENS._24,
		textAlign: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	inputContainer: {
		alignItems: 'center',
		flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
		borderBottomColor: colors.grey200,
		borderBottomWidth: 2,
		paddingVertical: VERTICAL_DIMENS._8,
		marginBottom: VERTICAL_DIMENS._8
	},
	inputContainerError: {
		borderBottomColor: colors.danger
	},
	inputContainerSuccess: {
		borderBottomColor: colors.success
	},
	countryCodeText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._16,
		marginLeft: HORIZONTAL_DIMENS._12,
		marginRight: HORIZONTAL_DIMENS._8
	},
	mobileInput: {
		flex: 1,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._16,
		height: VERTICAL_DIMENS._48,
		paddingHorizontal: HORIZONTAL_DIMENS._8
	},
	validationIcon: {
		width: 24,
		alignItems: 'center',
		justifyContent: 'center'
	},
	errorMessage: {
		color: colors.danger,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	successMessage: {
		color: colors.success,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: 'center'
	},
	helperText: {
		color: colors.grey500,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		textAlign: 'center',
		marginBottom: VERTICAL_DIMENS._24,
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		lineHeight: 16
	},
	confirmButton: {
		marginTop: VERTICAL_DIMENS._20,
		marginHorizontal: HORIZONTAL_DIMENS._24
	},
	confirmButtonText: {
		textTransform: 'uppercase'
	}
});

export { ShippingNumberEnhanced };
