import React, { memo, useCallback } from 'react';
import {
	StyleSheet,
	Text,
	TouchableOpacity,
	View,
	ActivityIndicator,
	FlatList
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Geocoder } from '../../../../utils/functions';
import { colors, fonts } from '../../../../utils/theme';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../../constants';
import { PlaceResult } from '../types';

interface PlacesSearchResultsEnhancedProps {
	results: PlaceResult[];
	onSelect: (location: { lat: number; lng: number }) => void;
	isLoading?: boolean;
	error?: string | null;
	query?: string;
}

const PlacesSearchResultsEnhanced: React.FC<PlacesSearchResultsEnhancedProps> = memo(({
	results,
	onSelect,
	isLoading = false,
	error = null,
	query = ''
}) => {
	const { t } = useTranslation();

	const handlePlaceSelect = useCallback(async (place: PlaceResult) => {
		try {
			const response = await Geocoder.from({ place_id: place.place_id });
			const location = response.results[0].geometry.location;
			onSelect(location);
		} catch (err) {
			console.error('Place selection error:', err);
		}
	}, [onSelect]);

	const renderPlaceItem = useCallback(({ item }: { item: PlaceResult }) => (
		<TouchableOpacity
			style={styles.resultItem}
			onPress={() => handlePlaceSelect(item)}
			accessibilityLabel={`Select ${item.description}`}
			accessibilityRole="button"
		>
			<View style={styles.resultContent}>
				<Text style={styles.mainText} numberOfLines={1}>
					{item.structured_formatting?.main_text || item.description}
				</Text>
				{item.structured_formatting?.secondary_text && (
					<Text style={styles.secondaryText} numberOfLines={1}>
						{item.structured_formatting.secondary_text}
					</Text>
				)}
			</View>
		</TouchableOpacity>
	), [handlePlaceSelect]);

	const renderEmptyState = useCallback(() => {
		if (isLoading) {
			return (
				<View style={styles.emptyState}>
					<ActivityIndicator size="small" color={colors.primary} />
					<Text style={styles.emptyText}>{t('searching_locations')}</Text>
				</View>
			);
		}

		if (error) {
			return (
				<View style={styles.emptyState}>
					<Text style={styles.errorText}>{error}</Text>
				</View>
			);
		}

		if (query.length > 0 && results.length === 0) {
			return (
				<View style={styles.emptyState}>
					<Text style={styles.emptyText}>{t('no_locations_found')}</Text>
				</View>
			);
		}

		return null;
	}, [isLoading, error, query, results.length, t]);

	if (!isLoading && !error && results.length === 0 && query.length === 0) {
		return null;
	}

	return (
		<View style={styles.container}>
			{results.length > 0 ? (
				<FlatList
					data={results}
					renderItem={renderPlaceItem}
					keyExtractor={(item) => item.place_id}
					style={styles.resultsList}
					showsVerticalScrollIndicator={false}
					keyboardShouldPersistTaps="handled"
					maxToRenderPerBatch={10}
					windowSize={10}
				/>
			) : (
				renderEmptyState()
			)}
		</View>
	);
});

PlacesSearchResultsEnhanced.displayName = 'PlacesSearchResultsEnhanced';

const styles = StyleSheet.create({
	container: {
		position: 'absolute',
		top: 45,
		left: 0,
		right: 0,
		backgroundColor: colors.white,
		borderRadius: 12,
		maxHeight: 280,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 4
		},
		shadowOpacity: 0.15,
		shadowRadius: 8,
		elevation: 8,
		zIndex: 1001
	},
	resultsList: {
		paddingVertical: VERTICAL_DIMENS._8
	},
	resultItem: {
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._12,
		borderBottomWidth: 1,
		borderBottomColor: colors.grey200
	},
	resultContent: {
		flex: 1
	},
	mainText: {
		color: colors.primary,
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '500',
		marginBottom: 2
	},
	secondaryText: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		fontWeight: '400'
	},
	emptyState: {
		alignItems: 'center',
		justifyContent: 'center',
		paddingVertical: VERTICAL_DIMENS._20,
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	emptyText: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center',
		marginTop: 8
	},
	errorText: {
		color: colors.danger,
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		textAlign: 'center'
	}
});

export { PlacesSearchResultsEnhanced };
