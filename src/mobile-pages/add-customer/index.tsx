import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Alert, Text, TouchableOpacity, View } from 'react-native';
import { Accordion, CheckBox, DropdownMobile, PrimaryButton, Switch, TextField } from '../../components/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { CustomerAddressInfo, CustomerAddSuccess, SalesPersonDropdown } from '../../components/mobile';
import { SelectLocationEnhanced } from '../../components/mobile/select-location-enhanced';
import { UserExistsModal, VerifyNumberMobile } from '../../components/modals';
import { CheckMark, PlusVector } from '../../assets/svgs/icons';
import { addCustomer, customerNumberVerify, getAllCustomers, getExistsCustomers } from '../../redux/apis/customer';
import { getLanguages, getSalesPersonId, getTenantCountry, getUserType } from '../../redux/selectors';
import { useAppSelector } from '../../redux/hooks';
import { validatePhoneNumber } from '../../utils/libphonenumber';
import { customerAddNotifications, USER_TYPES } from '../../constants';
import { checkBadRequest } from '../../utils/helpers';
import { colors } from '../../utils/theme';
import styles from './styles';

const AddCustomer = () => {
	const { t } = useTranslation();
	const { bottom } = useSafeAreaInsets();
	const dispatch = useDispatch();
	const successDialogRef = useRef<any>(null);
	const userType = useAppSelector(getUserType);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const tenantCountry = useAppSelector(getTenantCountry);
	const preferredLanguages = useAppSelector(getLanguages);
	const { loading, priceList, salesPersons } = useAppSelector(state => state.customer);
	//console.log('currentRole.portal_type', currentRole.portal_type);
	//console.log('salesPersons', salesPersons);
	const masterSettings = useAppSelector(state => state.setting.masterSettings);

	const [showOTPModal, setShowOTPModal] = useState(false);
	// const [addSuccessModal, setAddSuccessModal] = useState(false);
	const [userExists, setUserExits] = useState<Array<any>>([]);
	const [showLocationModal, setShowLocationModal] = useState(false);
	const [mobile, setMobile] = useState({ value: '', error: false, isValid: false, verified: false });
	const [verifiedMobile, setVerifiedMobile] = useState('');
	const [customerType, setCustomerType] = useState<any>(priceList[0]);
	const [salesPerson, setSalesPerson] = useState<any>(salesPersons[0]);
	const [preferredLanguage, setPreferredLanguage] = useState<any>(preferredLanguages[0]);
	const [shippingDetails, setShippingDetails] = useState<any>(null);
	const [details, setDetails] = useState({
		customerName: '',
		legalName: '',
		firstName: '',
		lastName: '',
		email: '',
		customerAppRequest: false,
		customerAppAccess: false
	});
	const [selection, setSelection] = useState({ start: 0, end: 0 });
	const [validForm, setValidForm] = useState(false);

	//console.log('salesPerson', salesPerson);

	useEffect(() => {
		if (priceList.length === 0) {
			Alert.alert(t('please_add_price_list'));
		};
		if (userType !== USER_TYPES.SALES_APP && salesPersons.length === 0) {
			Alert.alert(t('please_add_sales_person'));
		};
	}, [priceList, salesPersons]);

	useEffect(() => {
		if (isEmpty(details.customerName) || isEmpty(details.legalName) || isEmpty(details.firstName) || isEmpty(details.lastName)) {
			setValidForm(false);
			return;
		}
		if (mobile.isValid === false || mobile.verified === false || shippingDetails === null) {
			setValidForm(false);
			return;
		}
		if (priceList.length === 0) {
			setValidForm(false);
			return;
		}
		setValidForm(true);
	}, [details, mobile, shippingDetails, priceList]);

	const isEmpty = (value: string) => {
		const trimmedValue = value.trim();
		if (trimmedValue === '' || trimmedValue === null || trimmedValue === undefined) {
			return true;
		}
		return false;
	};

	const onToggleOtpModal = () => {
		setShowOTPModal(!showOTPModal);
	};

	const toggleLocationModal = () => {
		setShowLocationModal(!showLocationModal);
	};

	const onChangeDetails = (key: string, value: string) => {
		if (key === 'email' && value) {
			value = value.toLowerCase();
		}
		setDetails({ ...details, [key]: value });
	};

	const onChangeMobile = (text: string) => {
		const mobileNumber = text.replace(/[^0-9]/g, '');
		const isValid = validatePhoneNumber(tenantCountry.country_code, mobileNumber);
		const isVerify = mobileNumber === verifiedMobile;
		setMobile({ ...mobile, value: mobileNumber, isValid: !!isValid, verified: isVerify });
		setSelection({ ...selection, start: mobileNumber.length, end: mobileNumber.length });
	};

	const handleSelection = () => {
		//console.log('mobile.value.length', mobile.value.length);
		setSelection({ ...selection, start: mobile.value.length, end: mobile.value.length });
	};

	const onChangeAppAccess = (isSwitch: boolean) => {
		if (isSwitch) setDetails({ ...details, customerAppAccess: !details.customerAppAccess });
		else setDetails({ ...details, customerAppRequest: !details.customerAppRequest });
	};

	/* Send verification code to mobile number */
	const verifyMobileNumber = async () => {
		let number = mobile.value;
		const result = number.startsWith('0');
		result === true ? number = number.substring(1) : number;
		const requestBody = {
			action: 'SEND_OTP',
			mobileNumber: number,
			countryCode: tenantCountry.country_code
		};
		const response = await dispatch(customerNumberVerify(requestBody));
		if (!response.error) {
			onToggleOtpModal();
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onMobileVerifySuccess = () => {
		setMobile({ ...mobile, verified: true });
		setVerifiedMobile(mobile.value);
		onToggleOtpModal();
	};

	const onSelectLocation = (locationDetails: any) => {
		setShippingDetails(locationDetails);
		toggleLocationModal();
	};

	const checkCustomerExists = async () => {
		const requestBody = {
			tenantId: currentRole?.tenant_id?._id,
			countryCode: tenantCountry.country_code,
			mobileNumber: mobile.value
		};
		const response = await dispatch(getExistsCustomers(requestBody));
		if (!response.error) {
			if (response.payload.data && response.payload.data.length > 0) {
				setUserExits(response.payload.data);
				//setUserExits(customerExists);
			} else {
				onAddCustomer();
			}
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onConfirmAdd = () => {
		setUserExits([]);
		onAddCustomer();
	};

	const onAddCustomer = async () => {
		const salesId = userType !== USER_TYPES.SALES_APP ? salesPerson.value : salesPersonId;
		const requestBody = {
			...details,
			...shippingDetails,
			salesPersonId: salesId,
			shippingCountryId: tenantCountry._id,
			notifications: customerAddNotifications,
			countryCode: tenantCountry.country_code,
			deviceAccess: [],
			isActive: true,
			tenantId: currentRole?.tenant_id?._id,
			mobileNumber: mobile.value,
			preferredLanguage: preferredLanguage.value,
			priceListId: customerType.value,
			isVerified: true,
			customerCatalogMode: masterSettings?.catalog_mode ? true : false
		};
		if (requestBody.email === '') delete requestBody.email;
		const validRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
		if (requestBody.email && validRegex.test(requestBody.email.toLowerCase()) === false) {
			Alert.alert(t('invalid_email'));
			return;
		}
		const response = await dispatch(addCustomer(requestBody));
		if (!response.error) {
			// setAddSuccessModal(true); // Show success bottom sheet
			successDialogRef.current.showSuccessDialog({ ...requestBody, ...response.payload.data });
			const requestBody1 = {
				tenantId: currentRole.tenant_id?._id,
				salesPersonId,
				customerType: 'ALL',
				type: 'ALL',
				status: 'ACTIVE',
				sortByName: true,
				searchKey: '',
				supervisorId: ''
			};
			if (userType === USER_TYPES.SUPERVISOR_APP) {
				requestBody1.supervisorId = currentRole.user_id;
			}
			dispatch(getAllCustomers(requestBody1)); // Refresh customers list
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	return (
		<>
			<View style={styles.top}>
				<KeyboardAwareScrollView
					style={styles.scrollContainer}
					showsVerticalScrollIndicator={false}
					extraHeight={-64}
					keyboardShouldPersistTaps={'handled'}
				>
					<Accordion
						title={t('company_information')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={styles.accordionContent}>
							<TextField
								label={t('customer_name')}
								required
								value={details.customerName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('customerName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('legal_name')}
								required
								value={details.legalName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('legalName', value)}
								returnKeyType="done"
							/>

							{userType !== USER_TYPES.SALES_APP &&
								<SalesPersonDropdown
									salesPerson={salesPerson}
									salesPersons={salesPersons}
									setSalesPerson={setSalesPerson}
								/>
							}
							<View style={styles.inputSpacingTop} />

							<DropdownMobile
								label={t('customer_type')}
								required
								value={customerType}
								options={priceList}
								onChange={setCustomerType}
								labelContainer={styles.inputLabelContainer}
							/>
						</View>
					</Accordion>
					<Accordion
						title={t('contact_person')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={styles.accordionContent}>
							<TextField
								label={t('first_name')}
								required
								value={details.firstName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('firstName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('last_name')}
								required
								value={details.lastName}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('lastName', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<TextField
								label={t('email')}
								value={details.email}
								style={styles.inputField}
								labelContainer={styles.inputLabelContainer}
								labelStyle={styles.inputFieldLabel}
								onChangeText={(value) => onChangeDetails('email', value)}
								returnKeyType="done"
							/>
							<View style={styles.inputSpacingTop} />
							<DropdownMobile
								label={t('preferred_language')}
								required
								value={preferredLanguage}
								options={preferredLanguages}
								onChange={setPreferredLanguage}
								labelContainer={styles.inputLabelContainer}
							/>
							<View style={styles.mobileRow}>
								<View style={styles.mobileCountryCode}>
									<Text style={styles.mobileCountryCodeTxt}>{tenantCountry.country_code}</Text>
								</View>
								<TextField
									inputContainerStyle={styles.mobileNumber}
									required
									label={t('mobile_number')}
									selection={selection}
									onFocus={handleSelection}
									value={mobile.value}
									placeholder="5xxx"
									style={styles.mobileNumberInput}
									labelContainer={styles.inputLabelContainer}
									labelStyle={styles.inputFieldLabel}
									keyboardType="number-pad"
									onChangeText={onChangeMobile}
									returnKeyType="done"
								/>
								{
									mobile.isValid && <View style={styles.verifyPhone}>
										<CheckMark fill={colors.secondary} />
										{
											mobile.verified && <Text style={styles.verifyPhoneTxt}>{t('verified')}</Text>
										}
									</View>
								}
							</View>
							{
								!mobile.verified && <PrimaryButton
									title={t('verify_number')}
									onPress={verifyMobileNumber}
									style={styles.verifyButton}
									titleStyle={styles.verifyButtonTxt}
									disabled={!mobile.isValid}
									loading={loading}
								/>
							}
							{
								// Show only if mobile verified and customer app access master setting is true
								mobile.verified && (masterSettings?.customer_app_access ? (
									<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(true)}>
										<Text style={styles.requestAppAccessText}>{t('request_access')}</Text>

										<Switch
											onValueChange={() => onChangeAppAccess(true)}
											value={details.customerAppAccess}
										/>
									</TouchableOpacity>
								) : (
									<TouchableOpacity style={styles.requestAppAccess} onPress={() => onChangeAppAccess(false)}>
										<Text style={styles.requestAppAccessText}>{t('app_access')}</Text>


										<View style={styles.flexRow}>
											<Text style={styles.requestAppAccessText}>{t('request_access') + '  '}</Text>

											<CheckBox checked={details.customerAppRequest} onChange={() => onChangeAppAccess(false)} />
										</View>
									</TouchableOpacity>))
							}
							<VerifyNumberMobile
								countryCode={tenantCountry.country_code}
								mobileNumber={mobile.value}
								isVisible={showOTPModal}
								onCancel={onToggleOtpModal}
								onVerified={onMobileVerifySuccess}
							/>
						</View>
					</Accordion>
					<Accordion
						title={t('address')}
						accordionStyle={styles.accordionStyle}
						titleStyle={styles.accordionTitle}
					>
						<View style={[styles.accordionContent, styles.addressContent]}>
							{
								!shippingDetails && <TouchableOpacity
									style={styles.addLocationBtn}
									onPress={toggleLocationModal}
								// onPress={() => successDialogRef.current.showSuccessDialog()}
								>
									<View style={styles.plusCircle}>
										<PlusVector fill={colors.white} />
									</View>
									<Text style={styles.addLocationTxt}>{t('add_location')}</Text>
								</TouchableOpacity>
							}
							{
								shippingDetails && <CustomerAddressInfo
									addressInfo={shippingDetails}
									upperContainer={true}
									onEdit={toggleLocationModal}
								/>
							}
							<SelectLocationEnhanced
								isVisible={showLocationModal}
								onSelect={onSelectLocation}
								onClose={toggleLocationModal}
								title={t('select_customer_location')}
								showShippingStep={true}
							/>
						</View>
					</Accordion>
				</KeyboardAwareScrollView>
				<UserExistsModal
					users={userExists}
					onCancel={() => setUserExits([])}
					onConfirm={onConfirmAdd}
				/>
			</View>
			<View style={styles.bottom}>
				<PrimaryButton
					title={t('add_customer')}
					onPress={checkCustomerExists}
					style={[styles.addCustomerBtn, bottom > 0 && { marginBottom: bottom }]}
					titleStyle={styles.addCustomerBtnText}
					disabled={(!validForm)}
					loading={loading}
				/>
			</View>
			<CustomerAddSuccess ref={successDialogRef} />
		</>
	);
};

export default AddCustomer;
