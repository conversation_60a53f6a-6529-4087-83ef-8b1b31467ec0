# Enhanced SelectLocation Component

A modern, performant, and user-friendly location selection component for React Native applications.

## Features

### 🚀 Performance Optimizations
- **Debounced Search**: Reduces API calls with intelligent search debouncing
- **Memoized Components**: Prevents unnecessary re-renders
- **Throttled Map Interactions**: Optimized map region changes
- **Lazy Loading**: Components load only when needed

### 🎨 Enhanced User Experience
- **Smooth Animations**: Fluid transitions and loading states
- **Better Error Handling**: Comprehensive error states with retry options
- **Accessibility Support**: Full screen reader and keyboard navigation support
- **Loading States**: Skeleton screens and progress indicators
- **Offline Support**: Cached locations for offline usage

### 🏗️ Modern Architecture
- **TypeScript**: Full type safety and IntelliSense support
- **Custom Hooks**: Reusable location management logic
- **Separation of Concerns**: Clean, maintainable code structure
- **Error Boundaries**: Graceful error handling

### 📱 Enhanced Features
- **Recent Locations**: Quick access to previously selected locations
- **Location Caching**: Improved performance with smart caching
- **Better Search**: Enhanced search with structured results
- **Map Controls**: Improved map interaction controls
- **Validation**: Real-time input validation and feedback

## Usage

### Basic Usage

```tsx
import React, { useState } from 'react';
import { SelectLocationEnhanced } from './components/mobile/select-location-enhanced';

const MyComponent = () => {
  const [isVisible, setIsVisible] = useState(false);

  const handleLocationSelect = (locationData) => {
    console.log('Selected location:', locationData);
    setIsVisible(false);
  };

  return (
    <SelectLocationEnhanced
      isVisible={isVisible}
      onClose={() => setIsVisible(false)}
      onSelect={handleLocationSelect}
    />
  );
};
```

### Advanced Usage

```tsx
import React, { useState } from 'react';
import { SelectLocationEnhanced } from './components/mobile/select-location-enhanced';

const AdvancedExample = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [initialLocation, setInitialLocation] = useState({
    latitude: 37.78825,
    longitude: -122.4324
  });

  const handleLocationSelect = (locationData) => {
    console.log('Location data:', {
      address: locationData.shippingAddress,
      coordinates: {
        lat: locationData.latitude,
        lng: locationData.longitude
      },
      city: locationData.city,
      region: locationData.region,
      phone: locationData.shippingMobileNumber
    });
    setIsVisible(false);
  };

  return (
    <SelectLocationEnhanced
      isVisible={isVisible}
      onClose={() => setIsVisible(false)}
      onSelect={handleLocationSelect}
      customerRoleId="customer_123"
      shippingNumber="+1234567890"
      initialLocation={initialLocation}
      title="Select Delivery Location"
      showShippingStep={true}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isVisible` | `boolean` | - | Controls modal visibility |
| `onClose` | `() => void` | - | Called when modal should close |
| `onSelect` | `(data: LocationSelectionData) => void` | - | Called when location is selected |
| `customerRoleId` | `string` | - | Customer ID for saving to database |
| `shippingNumber` | `string` | - | Pre-filled shipping number |
| `initialLocation` | `LocationCoordinates` | - | Initial map location |
| `title` | `string` | `'Select Location'` | Modal title |
| `showShippingStep` | `boolean` | `true` | Show shipping number step |

## Types

### LocationSelectionData
```typescript
interface LocationSelectionData extends AddressDetails, LocationCoordinates {
  shippingMobileNumber?: string;
  shippingCountryCode?: string;
}
```

### LocationCoordinates
```typescript
interface LocationCoordinates {
  latitude: number;
  longitude: number;
}
```

### AddressDetails
```typescript
interface AddressDetails {
  city: string;
  shippingCityId: string;
  region: string;
  shippingRegionId: string;
  country: string;
  shippingAddress: string;
  regionCode: string;
}
```

## Custom Hooks

### useLocationManager
Manages location state, geocoding, and user location detection.

```tsx
const {
  locationState,
  updateRegion,
  getCurrentUserLocation,
  animateToLocation,
  isLoading,
  hasError,
  errorMessage
} = useLocationManager(initialLocation);
```

### useLocationSearch
Handles location search with debouncing and caching.

```tsx
const {
  searchState,
  updateQuery,
  clearSearch,
  isSearching,
  hasResults
} = useLocationSearch();
```

### useMapInteraction
Manages map interaction states and animations.

```tsx
const {
  mapState,
  setMapReady,
  startAnimation,
  canUpdateRegion,
  shouldShowLoadingIndicator
} = useMapInteraction();
```

## Utilities

### locationUtils
Comprehensive location utilities for distance calculation, caching, and validation.

```tsx
import { locationUtils } from './utils/locationUtils';

// Calculate distance between coordinates
const distance = locationUtils.calculateDistance(coord1, coord2);

// Cache location for offline use
await locationUtils.cacheLocation(coordinates, addressDetails);

// Get recent locations
const recent = await locationUtils.getRecentLocations();
```

## Performance Tips

1. **Use initialLocation**: Provide initial coordinates to avoid unnecessary location requests
2. **Enable Caching**: Location caching improves performance for repeat usage
3. **Optimize Search**: The component automatically debounces search queries
4. **Memory Management**: Components are memoized to prevent unnecessary re-renders

## Accessibility

The component includes comprehensive accessibility features:
- Screen reader support with proper labels
- Keyboard navigation
- High contrast support
- Focus management
- Semantic HTML structure

## Error Handling

The component includes robust error handling:
- Network connectivity issues
- Location permission errors
- Geocoding failures
- API errors
- Component crashes (Error Boundary)

## Browser Support

- iOS 11+
- Android 5.0+ (API level 21+)
- React Native 0.60+

## Dependencies

- `react-native-maps`: Map functionality
- `@gorhom/bottom-sheet`: Bottom sheet component
- `react-native-geolocation-service`: Location services
- `@react-native-async-storage/async-storage`: Local storage
- `react-native-modal`: Modal component

## Migration from Original SelectLocation

The enhanced component is designed to be a drop-in replacement:

```tsx
// Before
import { SelectLocation } from './components/mobile/select-location/SelectLocation';

// After
import { SelectLocationEnhanced as SelectLocation } from './components/mobile/select-location-enhanced';
```

All existing props are supported with additional enhancements.
